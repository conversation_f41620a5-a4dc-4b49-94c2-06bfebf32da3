"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRendererMotionComponent: () => (/* binding */ createRendererMotionComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../context/LazyContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/MotionContext/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/MotionContext/create.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/create.mjs\");\n/* harmony import */ var _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/is-browser.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n/* harmony import */ var _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./features/definitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n/* harmony import */ var _features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./features/load-features.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/load-features.mjs\");\n/* harmony import */ var _utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/symbol.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n/* harmony import */ var _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/use-motion-ref.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\");\n/* harmony import */ var _utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/use-visual-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\");\n/* __next_internal_client_entry_do_not_use__ createRendererMotionComponent auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */ function createRendererMotionComponent(param) {\n    let { preloadedFeatures, createVisualElement, useRender, useVisualState, Component } = param;\n    var _s = $RefreshSig$();\n    preloadedFeatures && (0,_features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__.loadFeatures)(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        _s();\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */ let MeasureLayout;\n        const configAndProps = {\n            ...(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props)\n        };\n        const { isStatic } = configAndProps;\n        const context = (0,_context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__.useCreateMotionContext)(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__.isBrowser) {\n            useStrictMode(configAndProps, preloadedFeatures);\n            const layoutProjection = getProjectionFunctionality(configAndProps);\n            MeasureLayout = layoutProjection.MeasureLayout;\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */ context.visualElement = (0,_utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__.useVisualElement)(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */ return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__.MotionContext.Provider, {\n            value: context,\n            children: [\n                MeasureLayout && context.visualElement ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MeasureLayout, {\n                    visualElement: context.visualElement,\n                    ...configAndProps\n                }) : null,\n                useRender(Component, props, (0,_utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__.useMotionRef)(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)\n            ]\n        });\n    }\n    _s(MotionComponent, \"OzmmWP8E2WLE0LhHHUY21ioDbYk=\", false, function() {\n        return [\n            useLayoutId,\n            _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__.useCreateMotionContext,\n            useVisualState,\n            _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__.useMotionRef,\n            useRender\n        ];\n    });\n    var _Component_displayName, _ref;\n    MotionComponent.displayName = \"motion.\".concat(typeof Component === \"string\" ? Component : \"create(\".concat((_ref = (_Component_displayName = Component.displayName) !== null && _Component_displayName !== void 0 ? _Component_displayName : Component.name) !== null && _ref !== void 0 ? _ref : \"\", \")\"));\n    const ForwardRefMotionComponent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(MotionComponent);\n    ForwardRefMotionComponent[_utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__.motionComponentSymbol] = Component;\n    return ForwardRefMotionComponent;\n}\nfunction useLayoutId(param) {\n    let { layoutId } = param;\n    _s();\n    const layoutGroupId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__.LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined ? layoutGroupId + \"-\" + layoutId : layoutId;\n}\n_s(useLayoutId, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n    _s1();\n    const isStrict = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__.LazyContext).strict;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */ if ( true && preloadedFeatures && isStrict) {\n        const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n        configAndProps.ignoreStrict ? (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.warning)(false, strictMessage) : (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.invariant)(false, strictMessage);\n    }\n}\n_s1(useStrictMode, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction getProjectionFunctionality(props) {\n    const { drag, layout } = _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__.featureDefinitions;\n    if (!drag && !layout) return {};\n    const combined = {\n        ...drag,\n        ...layout\n    };\n    return {\n        MeasureLayout: (drag === null || drag === void 0 ? void 0 : drag.isEnabled(props)) || (layout === null || layout === void 0 ? void 0 : layout.isEnabled(props)) ? combined.MeasureLayout : undefined,\n        ProjectionNode: combined.ProjectionNode\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/app-dir/link.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/image-component.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/client/image-component.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"Image\", ({\n    enumerable: true,\n    get: function() {\n        return Image;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _head = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../shared/lib/head */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js\"));\nconst _getimgprops = __webpack_require__(/*! ../shared/lib/get-img-props */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js\");\nconst _imageconfig = __webpack_require__(/*! ../shared/lib/image-config */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js\");\nconst _imageconfigcontextsharedruntime = __webpack_require__(/*! ../shared/lib/image-config-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js\");\nconst _warnonce = __webpack_require__(/*! ../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _imageloader = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! next/dist/shared/lib/image-loader */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js\"));\nconst _usemergedref = __webpack_require__(/*! ./use-merged-ref */ \"(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\n// This is replaced by webpack define plugin\nconst configEnv = {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":true,\"unoptimized\":false,\"domains\":[],\"remotePatterns\":[{\"protocol\":\"https\",\"hostname\":\"raw.githubusercontent.com\",\"port\":\"\",\"pathname\":\"/lobehub/lobe-icons/**\"},{\"protocol\":\"https\",\"hostname\":\"registry.npmmirror.com\",\"port\":\"\",\"pathname\":\"/@lobehub/icons-static-png/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@latest/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"cdn.jsdelivr.net\",\"port\":\"\",\"pathname\":\"/npm/simple-icons@v11/icons/**\"},{\"protocol\":\"https\",\"hostname\":\"images.unsplash.com\",\"port\":\"\",\"pathname\":\"/**\"},{\"protocol\":\"https\",\"hostname\":\"cloud.gmelius.com\",\"port\":\"\",\"pathname\":\"/public/logos/**\"},{\"protocol\":\"https\",\"hostname\":\"upload.wikimedia.org\",\"port\":\"\",\"pathname\":\"/wikipedia/commons/**\"},{\"protocol\":\"https\",\"hostname\":\"kstatic.googleusercontent.com\",\"port\":\"\",\"pathname\":\"/files/**\"}]};\nif (false) {}\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(img, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete, unoptimized, sizesInput) {\n    const src = img == null ? void 0 : img.src;\n    if (!img || img['data-loaded-src'] === src) {\n        return;\n    }\n    img['data-loaded-src'] = src;\n    const p = 'decode' in img ? img.decode() : Promise.resolve();\n    p.catch(()=>{}).then(()=>{\n        if (!img.parentElement || !img.isConnected) {\n            // Exit early in case of race condition:\n            // - onload() is called\n            // - decode() is called but incomplete\n            // - unmount is called\n            // - decode() completes\n            return;\n        }\n        if (placeholder !== 'empty') {\n            setBlurComplete(true);\n        }\n        if (onLoadRef == null ? void 0 : onLoadRef.current) {\n            // Since we don't have the SyntheticEvent here,\n            // we must create one with the same shape.\n            // See https://reactjs.org/docs/events.html\n            const event = new Event('load');\n            Object.defineProperty(event, 'target', {\n                writable: false,\n                value: img\n            });\n            let prevented = false;\n            let stopped = false;\n            onLoadRef.current({\n                ...event,\n                nativeEvent: event,\n                currentTarget: img,\n                target: img,\n                isDefaultPrevented: ()=>prevented,\n                isPropagationStopped: ()=>stopped,\n                persist: ()=>{},\n                preventDefault: ()=>{\n                    prevented = true;\n                    event.preventDefault();\n                },\n                stopPropagation: ()=>{\n                    stopped = true;\n                    event.stopPropagation();\n                }\n            });\n        }\n        if (onLoadingCompleteRef == null ? void 0 : onLoadingCompleteRef.current) {\n            onLoadingCompleteRef.current(img);\n        }\n        if (true) {\n            const origSrc = new URL(src, 'http://n').searchParams.get('url') || src;\n            if (img.getAttribute('data-nimg') === 'fill') {\n                if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n                    let widthViewportRatio = img.getBoundingClientRect().width / window.innerWidth;\n                    if (widthViewportRatio < 0.6) {\n                        if (sizesInput === '100vw') {\n                            (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes');\n                        } else {\n                            (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes');\n                        }\n                    }\n                }\n                if (img.parentElement) {\n                    const { position } = window.getComputedStyle(img.parentElement);\n                    const valid = [\n                        'absolute',\n                        'fixed',\n                        'relative'\n                    ];\n                    if (!valid.includes(position)) {\n                        (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has \"fill\" and parent element with invalid \"position\". Provided \"' + position + '\" should be one of ' + valid.map(String).join(',') + \".\");\n                    }\n                }\n                if (img.height === 0) {\n                    (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.');\n                }\n            }\n            const heightModified = img.height.toString() !== img.getAttribute('height');\n            const widthModified = img.width.toString() !== img.getAttribute('width');\n            if (heightModified && !widthModified || !heightModified && widthModified) {\n                (0, _warnonce.warnOnce)('Image with src \"' + origSrc + '\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles \\'width: \"auto\"\\' or \\'height: \"auto\"\\' to maintain the aspect ratio.');\n            }\n        }\n    });\n}\nfunction getDynamicProps(fetchPriority) {\n    if (Boolean(_react.use)) {\n        // In React 19.0.0 or newer, we must use camelCase\n        // prop to avoid \"Warning: Invalid DOM property\".\n        // See https://github.com/facebook/react/pull/25927\n        return {\n            fetchPriority\n        };\n    }\n    // In React 18.2.0 or older, we must use lowercase prop\n    // to avoid \"Warning: Invalid DOM property\".\n    return {\n        fetchpriority: fetchPriority\n    };\n}\nconst ImageElement = /*#__PURE__*/ (0, _react.forwardRef)((param, forwardedRef)=>{\n    let { src, srcSet, sizes, height, width, decoding, className, style, fetchPriority, placeholder, loading, unoptimized, fill, onLoadRef, onLoadingCompleteRef, setBlurComplete, setShowAltText, sizesInput, onLoad, onError, ...rest } = param;\n    const ownRef = (0, _react.useCallback)((img)=>{\n        if (!img) {\n            return;\n        }\n        if (onError) {\n            // If the image has an error before react hydrates, then the error is lost.\n            // The workaround is to wait until the image is mounted which is after hydration,\n            // then we set the src again to trigger the error handler (if there was an error).\n            // eslint-disable-next-line no-self-assign\n            img.src = img.src;\n        }\n        if (true) {\n            if (!src) {\n                console.error('Image is missing required \"src\" property:', img);\n            }\n            if (img.getAttribute('alt') === null) {\n                console.error('Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.');\n            }\n        }\n        if (img.complete) {\n            handleLoading(img, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete, unoptimized, sizesInput);\n        }\n    }, [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput\n    ]);\n    const ref = (0, _usemergedref.useMergedRef)(forwardedRef, ownRef);\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"img\", {\n        ...rest,\n        ...getDynamicProps(fetchPriority),\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading: loading,\n        width: width,\n        height: height,\n        decoding: decoding,\n        \"data-nimg\": fill ? 'fill' : '1',\n        className: className,\n        style: style,\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes: sizes,\n        srcSet: srcSet,\n        src: src,\n        ref: ref,\n        onLoad: (event)=>{\n            const img = event.currentTarget;\n            handleLoading(img, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete, unoptimized, sizesInput);\n        },\n        onError: (event)=>{\n            // if the real image fails to load, this will ensure \"alt\" is visible\n            setShowAltText(true);\n            if (placeholder !== 'empty') {\n                // If the real image fails to load, this will still remove the placeholder.\n                setBlurComplete(true);\n            }\n            if (onError) {\n                onError(event);\n            }\n        }\n    });\n});\nfunction ImagePreload(param) {\n    let { isAppRouter, imgAttributes } = param;\n    const opts = {\n        as: 'image',\n        imageSrcSet: imgAttributes.srcSet,\n        imageSizes: imgAttributes.sizes,\n        crossOrigin: imgAttributes.crossOrigin,\n        referrerPolicy: imgAttributes.referrerPolicy,\n        ...getDynamicProps(imgAttributes.fetchPriority)\n    };\n    if (isAppRouter && _reactdom.default.preload) {\n        // See https://github.com/facebook/react/pull/26940\n        _reactdom.default.preload(imgAttributes.src, opts);\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_head.default, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"preload\",\n            // Note how we omit the `href` attribute, as it would only be relevant\n            // for browsers that do not support `imagesrcset`, and in those cases\n            // it would cause the incorrect image to be preloaded.\n            //\n            // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n            href: imgAttributes.srcSet ? undefined : imgAttributes.src,\n            ...opts\n        }, '__nimg-' + imgAttributes.src + imgAttributes.srcSet + imgAttributes.sizes)\n    });\n}\n_c = ImagePreload;\nconst Image = /*#__PURE__*/ (0, _react.forwardRef)((props, forwardedRef)=>{\n    const pagesRouter = (0, _react.useContext)(_routercontextsharedruntime.RouterContext);\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const configContext = (0, _react.useContext)(_imageconfigcontextsharedruntime.ImageConfigContext);\n    const config = (0, _react.useMemo)(()=>{\n        var _c_qualities;\n        const c = configEnv || configContext || _imageconfig.imageConfigDefault;\n        const allSizes = [\n            ...c.deviceSizes,\n            ...c.imageSizes\n        ].sort((a, b)=>a - b);\n        const deviceSizes = c.deviceSizes.sort((a, b)=>a - b);\n        const qualities = (_c_qualities = c.qualities) == null ? void 0 : _c_qualities.sort((a, b)=>a - b);\n        return {\n            ...c,\n            allSizes,\n            deviceSizes,\n            qualities\n        };\n    }, [\n        configContext\n    ]);\n    const { onLoad, onLoadingComplete } = props;\n    const onLoadRef = (0, _react.useRef)(onLoad);\n    (0, _react.useEffect)(()=>{\n        onLoadRef.current = onLoad;\n    }, [\n        onLoad\n    ]);\n    const onLoadingCompleteRef = (0, _react.useRef)(onLoadingComplete);\n    (0, _react.useEffect)(()=>{\n        onLoadingCompleteRef.current = onLoadingComplete;\n    }, [\n        onLoadingComplete\n    ]);\n    const [blurComplete, setBlurComplete] = (0, _react.useState)(false);\n    const [showAltText, setShowAltText] = (0, _react.useState)(false);\n    const { props: imgAttributes, meta: imgMeta } = (0, _getimgprops.getImgProps)(props, {\n        defaultLoader: _imageloader.default,\n        imgConf: config,\n        blurComplete,\n        showAltText\n    });\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(ImageElement, {\n                ...imgAttributes,\n                unoptimized: imgMeta.unoptimized,\n                placeholder: imgMeta.placeholder,\n                fill: imgMeta.fill,\n                onLoadRef: onLoadRef,\n                onLoadingCompleteRef: onLoadingCompleteRef,\n                setBlurComplete: setBlurComplete,\n                setShowAltText: setShowAltText,\n                sizesInput: props.sizes,\n                ref: forwardedRef\n            }),\n            imgMeta.priority ? /*#__PURE__*/ (0, _jsxruntime.jsx)(ImagePreload, {\n                isAppRouter: isAppRouter,\n                imgAttributes: imgAttributes\n            }) : null\n        ]\n    });\n});\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=image-component.js.map\nvar _c;\n$RefreshReg$(_c, \"ImagePreload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2ltYWdlLWNvbXBvbmVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O3lDQTBXYUE7OztlQUFBQTs7Ozs7OzZFQS9WTjsrRUFDYzsyRUFDSjt5Q0FDVzt5Q0FZTzs2REFDQTtzQ0FDVjt3REFDSztrRkFHSjswQ0FDRztBQUU3Qiw0Q0FBNEM7QUFDNUMsTUFBTUMsWUFBWUMsdS9CQUE2QjtBQUUvQyxJQUFJLEtBQTZCLEVBQUUsRUFFbEM7QUFtQkQsMEVBQTBFO0FBQzFFLGlEQUFpRDtBQUNqRCxTQUFTTSxjQUNQQyxHQUEyQixFQUMzQkMsV0FBNkIsRUFDN0JDLFNBQXFELEVBQ3JEQyxvQkFBMkUsRUFDM0VDLGVBQXFDLEVBQ3JDQyxXQUFvQixFQUNwQkMsVUFBOEI7SUFFOUIsTUFBTUMsTUFBTVAsT0FBQUEsT0FBQUEsS0FBQUEsSUFBQUEsSUFBS08sR0FBRztJQUNwQixJQUFJLENBQUNQLE9BQU9BLEdBQUcsQ0FBQyxrQkFBa0IsS0FBS08sS0FBSztRQUMxQztJQUNGO0lBQ0FQLEdBQUcsQ0FBQyxrQkFBa0IsR0FBR087SUFDekIsTUFBTUMsSUFBSSxZQUFZUixNQUFNQSxJQUFJUyxNQUFNLEtBQUtDLFFBQVFDLE9BQU87SUFDMURILEVBQUVJLEtBQUssQ0FBQyxLQUFPLEdBQUdDLElBQUksQ0FBQztRQUNyQixJQUFJLENBQUNiLElBQUljLGFBQWEsSUFBSSxDQUFDZCxJQUFJZSxXQUFXLEVBQUU7WUFDMUMsd0NBQXdDO1lBQ3hDLHVCQUF1QjtZQUN2QixzQ0FBc0M7WUFDdEMsc0JBQXNCO1lBQ3RCLHVCQUF1QjtZQUN2QjtRQUNGO1FBQ0EsSUFBSWQsZ0JBQWdCLFNBQVM7WUFDM0JHLGdCQUFnQjtRQUNsQjtRQUNBLElBQUlGLGFBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFVBQVdjLE9BQU8sRUFBRTtZQUN0QiwrQ0FBK0M7WUFDL0MsMENBQTBDO1lBQzFDLDJDQUEyQztZQUMzQyxNQUFNQyxRQUFRLElBQUlDLE1BQU07WUFDeEJDLE9BQU9DLGNBQWMsQ0FBQ0gsT0FBTyxVQUFVO2dCQUFFSSxVQUFVO2dCQUFPQyxPQUFPdEI7WUFBSTtZQUNyRSxJQUFJdUIsWUFBWTtZQUNoQixJQUFJQyxVQUFVO1lBQ2R0QixVQUFVYyxPQUFPLENBQUM7Z0JBQ2hCLEdBQUdDLEtBQUs7Z0JBQ1JRLGFBQWFSO2dCQUNiUyxlQUFlMUI7Z0JBQ2YyQixRQUFRM0I7Z0JBQ1I0QixvQkFBb0IsSUFBTUw7Z0JBQzFCTSxzQkFBc0IsSUFBTUw7Z0JBQzVCTSxTQUFTLEtBQU87Z0JBQ2hCQyxnQkFBZ0I7b0JBQ2RSLFlBQVk7b0JBQ1pOLE1BQU1jLGNBQWM7Z0JBQ3RCO2dCQUNBQyxpQkFBaUI7b0JBQ2ZSLFVBQVU7b0JBQ1ZQLE1BQU1lLGVBQWU7Z0JBQ3ZCO1lBQ0Y7UUFDRjtRQUNBLElBQUk3Qix3QkFBQUEsT0FBQUEsS0FBQUEsSUFBQUEscUJBQXNCYSxPQUFPLEVBQUU7WUFDakNiLHFCQUFxQmEsT0FBTyxDQUFDaEI7UUFDL0I7UUFDQSxJQUFJUCxJQUFvQixFQUFtQjtZQUN6QyxNQUFNeUMsVUFBVSxJQUFJQyxJQUFJNUIsS0FBSyxZQUFZNkIsWUFBWSxDQUFDQyxHQUFHLENBQUMsVUFBVTlCO1lBQ3BFLElBQUlQLElBQUlzQyxZQUFZLENBQUMsaUJBQWlCLFFBQVE7Z0JBQzVDLElBQUksQ0FBQ2pDLGVBQWdCLEVBQUNDLGNBQWNBLGVBQWUsUUFBTSxFQUFJO29CQUMzRCxJQUFJaUMscUJBQ0Z2QyxJQUFJd0MscUJBQXFCLEdBQUdDLEtBQUssR0FBRzdDLE9BQU84QyxVQUFVO29CQUN2RCxJQUFJSCxxQkFBcUIsS0FBSzt3QkFDNUIsSUFBSWpDLGVBQWUsU0FBUzs0QkFDMUJxQyxDQUFBQSxHQUFBQSxVQUFBQSxRQUFBQSxFQUNHLHFCQUFrQlQsVUFBUTt3QkFFL0IsT0FBTzs0QkFDTFMsQ0FBQUEsR0FBQUEsVUFBQUEsUUFBQUEsRUFDRyxxQkFBa0JULFVBQVE7d0JBRS9CO29CQUNGO2dCQUNGO2dCQUNBLElBQUlsQyxJQUFJYyxhQUFhLEVBQUU7b0JBQ3JCLE1BQU0sRUFBRThCLFFBQVEsRUFBRSxHQUFHaEQsT0FBT2lELGdCQUFnQixDQUFDN0MsSUFBSWMsYUFBYTtvQkFDOUQsTUFBTWdDLFFBQVE7d0JBQUM7d0JBQVk7d0JBQVM7cUJBQVc7b0JBQy9DLElBQUksQ0FBQ0EsTUFBTUMsUUFBUSxDQUFDSCxXQUFXO3dCQUM3QkQsQ0FBQUEsR0FBQUEsVUFBQUEsUUFBQUEsRUFDRyxxQkFBa0JULFVBQVEsd0VBQXFFVSxXQUFTLHdCQUFxQkUsTUFDM0hFLEdBQUcsQ0FBQ0MsUUFDSkMsSUFBSSxDQUFDLE9BQUs7b0JBRWpCO2dCQUNGO2dCQUNBLElBQUlsRCxJQUFJbUQsTUFBTSxLQUFLLEdBQUc7b0JBQ3BCUixDQUFBQSxHQUFBQSxVQUFBQSxRQUFBQSxFQUNHLHFCQUFrQlQsVUFBUTtnQkFFL0I7WUFDRjtZQUVBLE1BQU1rQixpQkFDSnBELElBQUltRCxNQUFNLENBQUNFLFFBQVEsT0FBT3JELElBQUlzQyxZQUFZLENBQUM7WUFDN0MsTUFBTWdCLGdCQUFnQnRELElBQUl5QyxLQUFLLENBQUNZLFFBQVEsT0FBT3JELElBQUlzQyxZQUFZLENBQUM7WUFDaEUsSUFDR2Msa0JBQWtCLENBQUNFLGlCQUNuQixDQUFDRixrQkFBa0JFLGVBQ3BCO2dCQUNBWCxDQUFBQSxHQUFBQSxVQUFBQSxRQUFBQSxFQUNHLHFCQUFrQlQsVUFBUTtZQUUvQjtRQUNGO0lBQ0Y7QUFDRjtBQUVBLFNBQVNxQixnQkFDUEMsYUFBc0I7SUFFdEIsSUFBSUMsUUFBUUMsT0FBQUEsR0FBRyxHQUFHO1FBQ2hCLGtEQUFrRDtRQUNsRCxpREFBaUQ7UUFDakQsbURBQW1EO1FBQ25ELE9BQU87WUFBRUY7UUFBYztJQUN6QjtJQUNBLHVEQUF1RDtJQUN2RCw0Q0FBNEM7SUFDNUMsT0FBTztRQUFFRyxlQUFlSDtJQUFjO0FBQ3hDO0FBRUEsTUFBTUksZUFBQUEsV0FBQUEsR0FBZUMsQ0FBQUEsR0FBQUEsT0FBQUEsVUFBQUEsRUFDbkIsUUF3QkVDO1FBdkJBLEVBQ0V2RCxHQUFHLEVBQ0h3RCxNQUFNLEVBQ05DLEtBQUssRUFDTGIsTUFBTSxFQUNOVixLQUFLLEVBQ0x3QixRQUFRLEVBQ1JDLFNBQVMsRUFDVEMsS0FBSyxFQUNMWCxhQUFhLEVBQ2J2RCxXQUFXLEVBQ1htRSxPQUFPLEVBQ1AvRCxXQUFXLEVBQ1hnRSxJQUFJLEVBQ0puRSxTQUFTLEVBQ1RDLG9CQUFvQixFQUNwQkMsZUFBZSxFQUNma0UsY0FBYyxFQUNkaEUsVUFBVSxFQUNWaUUsTUFBTSxFQUNOQyxPQUFPLEVBQ1AsR0FBR0MsTUFDSjtJQUdELE1BQU1DLFNBQVNDLENBQUFBLEdBQUFBLE9BQUFBLFdBQUFBLEVBQ2IsQ0FBQzNFO1FBQ0MsSUFBSSxDQUFDQSxLQUFLO1lBQ1I7UUFDRjtRQUNBLElBQUl3RSxTQUFTO1lBQ1gsMkVBQTJFO1lBQzNFLGlGQUFpRjtZQUNqRixrRkFBa0Y7WUFDbEYsMENBQTBDO1lBQzFDeEUsSUFBSU8sR0FBRyxHQUFHUCxJQUFJTyxHQUFHO1FBQ25CO1FBQ0EsSUFBSWQsSUFBb0IsRUFBbUI7WUFDekMsSUFBSSxDQUFDYyxLQUFLO2dCQUNScUUsUUFBUUMsS0FBSyxDQUFFLDZDQUE0QzdFO1lBQzdEO1lBQ0EsSUFBSUEsSUFBSXNDLFlBQVksQ0FBQyxXQUFXLE1BQU07Z0JBQ3BDc0MsUUFBUUMsS0FBSyxDQUNWO1lBRUw7UUFDRjtRQUNBLElBQUk3RSxJQUFJOEUsUUFBUSxFQUFFO1lBQ2hCL0UsY0FDRUMsS0FDQUMsYUFDQUMsV0FDQUMsc0JBQ0FDLGlCQUNBQyxhQUNBQztRQUVKO0lBQ0YsR0FDQTtRQUNFQztRQUNBTjtRQUNBQztRQUNBQztRQUNBQztRQUNBb0U7UUFDQW5FO1FBQ0FDO0tBQ0Q7SUFHSCxNQUFNeUUsTUFBTUMsQ0FBQUEsR0FBQUEsY0FBQUEsWUFBQUEsRUFBYWxCLGNBQWNZO0lBRXZDLHFCQUNFLHFCQUFDMUUsT0FBQUE7UUFDRSxHQUFHeUUsSUFBSTtRQUNQLEdBQUdsQixnQkFBZ0JDLGNBQWM7UUFDbEMscUVBQXFFO1FBQ3JFLHdFQUF3RTtRQUN4RSxxREFBcUQ7UUFDckRZLFNBQVNBO1FBQ1QzQixPQUFPQTtRQUNQVSxRQUFRQTtRQUNSYyxVQUFVQTtRQUNWZ0IsYUFBV1osT0FBTyxTQUFTO1FBQzNCSCxXQUFXQTtRQUNYQyxPQUFPQTtRQUNQLHVFQUF1RTtRQUN2RSxtRUFBbUU7UUFDbkUseUVBQXlFO1FBQ3pFLDBFQUEwRTtRQUMxRSwyQkFBMkI7UUFDM0Isc0RBQXNEO1FBQ3RESCxPQUFPQTtRQUNQRCxRQUFRQTtRQUNSeEQsS0FBS0E7UUFDTHdFLEtBQUtBO1FBQ0xSLFFBQVEsQ0FBQ3REO1lBQ1AsTUFBTWpCLE1BQU1pQixNQUFNUyxhQUFhO1lBQy9CM0IsY0FDRUMsS0FDQUMsYUFDQUMsV0FDQUMsc0JBQ0FDLGlCQUNBQyxhQUNBQztRQUVKO1FBQ0FrRSxTQUFTLENBQUN2RDtZQUNSLHFFQUFxRTtZQUNyRXFELGVBQWU7WUFDZixJQUFJckUsZ0JBQWdCLFNBQVM7Z0JBQzNCLDJFQUEyRTtnQkFDM0VHLGdCQUFnQjtZQUNsQjtZQUNBLElBQUlvRSxTQUFTO2dCQUNYQSxRQUFRdkQ7WUFDVjtRQUNGOztBQUdOO0FBR0Ysc0JBQXNCLEtBTXJCO0lBTnFCLE1BQ3BCa0UsV0FBVyxFQUNYQyxhQUFhLEVBSWQsR0FOcUI7SUFPcEIsTUFBTUMsT0FBTztRQUNYQyxJQUFJO1FBQ0pDLGFBQWFILGNBQWNyQixNQUFNO1FBQ2pDeUIsWUFBWUosY0FBY3BCLEtBQUs7UUFDL0J5QixhQUFhTCxjQUFjSyxXQUFXO1FBQ3RDQyxnQkFBZ0JOLGNBQWNNLGNBQWM7UUFDNUMsR0FBR25DLGdCQUFnQjZCLGNBQWM1QixhQUFhLENBQUM7SUFDakQ7SUFFQSxJQUFJMkIsZUFBZVEsVUFBQUEsT0FBUSxDQUFDQyxPQUFPLEVBQUU7UUFDbkMsbURBQW1EO1FBQ25ERCxVQUFBQSxPQUFRLENBQUNDLE9BQU8sQ0FDZFIsY0FBYzdFLEdBQUcsRUFDakI7UUFHRixPQUFPO0lBQ1Q7SUFFQSxPQUNFLFdBREYsR0FDRSxxQkFBQ3NGLE1BQUFBLE9BQUk7a0JBQ0gsbUNBQUNDLFFBQUFBO1lBT0NDLEtBQUk7WUFDSixzRUFBc0U7WUFDdEUscUVBQXFFO1lBQ3JFLHNEQUFzRDtZQUN0RCxFQUFFO1lBQ0YsOEVBQThFO1lBQzlFQyxNQUFNWixjQUFjckIsTUFBTSxHQUFHa0MsWUFBWWIsY0FBYzdFLEdBQUc7WUFDekQsR0FBRzhFLElBQUk7V0FaTixZQUNBRCxjQUFjN0UsR0FBRyxHQUNqQjZFLGNBQWNyQixNQUFNLEdBQ3BCcUIsY0FBY3BCLEtBQUs7O0FBYTdCO0tBOUNTa0I7QUFxREYsTUFBTTNGLFFBQUFBLFdBQUFBLEdBQVFzRSxDQUFBQSxHQUFBQSxPQUFBQSxVQUFBQSxFQUNuQixDQUFDcUMsT0FBT3BDO0lBQ04sTUFBTXFDLGNBQWNDLENBQUFBLEdBQUFBLE9BQUFBLFVBQUFBLEVBQVdDLDRCQUFBQSxhQUFhO0lBQzVDLDBEQUEwRDtJQUMxRCxNQUFNbEIsY0FBYyxDQUFDZ0I7SUFFckIsTUFBTUcsZ0JBQWdCRixDQUFBQSxHQUFBQSxPQUFBQSxVQUFBQSxFQUFXRyxpQ0FBQUEsa0JBQWtCO0lBQ25ELE1BQU1DLFNBQVNDLENBQUFBLEdBQUFBLE9BQUFBLE9BQUFBLEVBQVE7WUFJSEM7UUFIbEIsTUFBTUEsSUFBSWxILGFBQWE4RyxpQkFBaUJLLGFBQUFBLGtCQUFrQjtRQUMxRCxNQUFNQyxXQUFXO2VBQUlGLEVBQUVHLFdBQVc7ZUFBS0gsRUFBRWxCLFVBQVU7U0FBQyxDQUFDc0IsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELElBQUlDO1FBQ3hFLE1BQU1ILGNBQWNILEVBQUVHLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1ELElBQUlDO1FBQ3JELE1BQU1DLFlBQUFBLGdCQUFZUCxFQUFFTyxTQUFBQSxLQUFTLGdCQUFYUCxhQUFhSSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7UUFDbEQsT0FBTztZQUFFLEdBQUdOLENBQUM7WUFBRUU7WUFBVUM7WUFBYUk7UUFBVTtJQUNsRCxHQUFHO1FBQUNYO0tBQWM7SUFFbEIsTUFBTSxFQUFFL0IsTUFBTSxFQUFFMkMsaUJBQWlCLEVBQUUsR0FBR2hCO0lBQ3RDLE1BQU1oRyxZQUFZaUgsQ0FBQUEsR0FBQUEsT0FBQUEsTUFBQUEsRUFBTzVDO0lBRXpCNkMsQ0FBQUEsR0FBQUEsT0FBQUEsU0FBQUEsRUFBVTtRQUNSbEgsVUFBVWMsT0FBTyxHQUFHdUQ7SUFDdEIsR0FBRztRQUFDQTtLQUFPO0lBRVgsTUFBTXBFLHVCQUF1QmdILENBQUFBLEdBQUFBLE9BQUFBLE1BQUFBLEVBQU9EO0lBRXBDRSxDQUFBQSxHQUFBQSxPQUFBQSxTQUFBQSxFQUFVO1FBQ1JqSCxxQkFBcUJhLE9BQU8sR0FBR2tHO0lBQ2pDLEdBQUc7UUFBQ0E7S0FBa0I7SUFFdEIsTUFBTSxDQUFDRyxjQUFjakgsZ0JBQWdCLEdBQUdrSCxDQUFBQSxHQUFBQSxPQUFBQSxRQUFBQSxFQUFTO0lBQ2pELE1BQU0sQ0FBQ0MsYUFBYWpELGVBQWUsR0FBR2dELENBQUFBLEdBQUFBLE9BQUFBLFFBQUFBLEVBQVM7SUFFL0MsTUFBTSxFQUFFcEIsT0FBT2QsYUFBYSxFQUFFb0MsTUFBTUMsT0FBTyxFQUFFLEdBQUdDLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVl4QixPQUFPO1FBQ2pFeUIsZUFBQUEsYUFBQUEsT0FBYTtRQUNiQyxTQUFTcEI7UUFDVGE7UUFDQUU7SUFDRjtJQUVBLE9BQ0UsV0FERixHQUNFOzswQkFFSSxxQkFBQzNELGNBQUFBO2dCQUNFLEdBQUd3QixhQUFhO2dCQUNqQi9FLGFBQWFvSCxRQUFRcEgsV0FBVztnQkFDaENKLGFBQWF3SCxRQUFReEgsV0FBVztnQkFDaENvRSxNQUFNb0QsUUFBUXBELElBQUk7Z0JBQ2xCbkUsV0FBV0E7Z0JBQ1hDLHNCQUFzQkE7Z0JBQ3RCQyxpQkFBaUJBO2dCQUNqQmtFLGdCQUFnQkE7Z0JBQ2hCaEUsWUFBWTRGLE1BQU1sQyxLQUFLO2dCQUN2QmUsS0FBS2pCOztZQUdSMkQsUUFBUUksUUFBUSxpQkFDZixxQkFBQzNDLGNBQUFBO2dCQUNDQyxhQUFhQTtnQkFDYkMsZUFBZUE7aUJBRWY7OztBQUdWIiwic291cmNlcyI6WyJDOlxcc3JjXFxjbGllbnRcXGltYWdlLWNvbXBvbmVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwge1xuICB1c2VSZWYsXG4gIHVzZUVmZmVjdCxcbiAgdXNlQ2FsbGJhY2ssXG4gIHVzZUNvbnRleHQsXG4gIHVzZU1lbW8sXG4gIHVzZVN0YXRlLFxuICBmb3J3YXJkUmVmLFxuICB1c2UsXG59IGZyb20gJ3JlYWN0J1xuaW1wb3J0IFJlYWN0RE9NIGZyb20gJ3JlYWN0LWRvbSdcbmltcG9ydCBIZWFkIGZyb20gJy4uL3NoYXJlZC9saWIvaGVhZCdcbmltcG9ydCB7IGdldEltZ1Byb3BzIH0gZnJvbSAnLi4vc2hhcmVkL2xpYi9nZXQtaW1nLXByb3BzJ1xuaW1wb3J0IHR5cGUge1xuICBJbWFnZVByb3BzLFxuICBJbWdQcm9wcyxcbiAgT25Mb2FkLFxuICBPbkxvYWRpbmdDb21wbGV0ZSxcbiAgUGxhY2Vob2xkZXJWYWx1ZSxcbn0gZnJvbSAnLi4vc2hhcmVkL2xpYi9nZXQtaW1nLXByb3BzJ1xuaW1wb3J0IHR5cGUge1xuICBJbWFnZUNvbmZpZ0NvbXBsZXRlLFxuICBJbWFnZUxvYWRlclByb3BzLFxufSBmcm9tICcuLi9zaGFyZWQvbGliL2ltYWdlLWNvbmZpZydcbmltcG9ydCB7IGltYWdlQ29uZmlnRGVmYXVsdCB9IGZyb20gJy4uL3NoYXJlZC9saWIvaW1hZ2UtY29uZmlnJ1xuaW1wb3J0IHsgSW1hZ2VDb25maWdDb250ZXh0IH0gZnJvbSAnLi4vc2hhcmVkL2xpYi9pbWFnZS1jb25maWctY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB7IHdhcm5PbmNlIH0gZnJvbSAnLi4vc2hhcmVkL2xpYi91dGlscy93YXJuLW9uY2UnXG5pbXBvcnQgeyBSb3V0ZXJDb250ZXh0IH0gZnJvbSAnLi4vc2hhcmVkL2xpYi9yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcblxuLy8gQHRzLWlnbm9yZSAtIFRoaXMgaXMgcmVwbGFjZWQgYnkgd2VicGFjayBhbGlhc1xuaW1wb3J0IGRlZmF1bHRMb2FkZXIgZnJvbSAnbmV4dC9kaXN0L3NoYXJlZC9saWIvaW1hZ2UtbG9hZGVyJ1xuaW1wb3J0IHsgdXNlTWVyZ2VkUmVmIH0gZnJvbSAnLi91c2UtbWVyZ2VkLXJlZidcblxuLy8gVGhpcyBpcyByZXBsYWNlZCBieSB3ZWJwYWNrIGRlZmluZSBwbHVnaW5cbmNvbnN0IGNvbmZpZ0VudiA9IHByb2Nlc3MuZW52Ll9fTkVYVF9JTUFHRV9PUFRTIGFzIGFueSBhcyBJbWFnZUNvbmZpZ0NvbXBsZXRlXG5cbmlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykge1xuICA7KGdsb2JhbFRoaXMgYXMgYW55KS5fX05FWFRfSU1BR0VfSU1QT1JURUQgPSB0cnVlXG59XG5cbmV4cG9ydCB0eXBlIHsgSW1hZ2VMb2FkZXJQcm9wcyB9XG5leHBvcnQgdHlwZSBJbWFnZUxvYWRlciA9IChwOiBJbWFnZUxvYWRlclByb3BzKSA9PiBzdHJpbmdcblxudHlwZSBJbWdFbGVtZW50V2l0aERhdGFQcm9wID0gSFRNTEltYWdlRWxlbWVudCAmIHtcbiAgJ2RhdGEtbG9hZGVkLXNyYyc6IHN0cmluZyB8IHVuZGVmaW5lZFxufVxuXG50eXBlIEltYWdlRWxlbWVudFByb3BzID0gSW1nUHJvcHMgJiB7XG4gIHVub3B0aW1pemVkOiBib29sZWFuXG4gIHBsYWNlaG9sZGVyOiBQbGFjZWhvbGRlclZhbHVlXG4gIG9uTG9hZFJlZjogUmVhY3QuTXV0YWJsZVJlZk9iamVjdDxPbkxvYWQgfCB1bmRlZmluZWQ+XG4gIG9uTG9hZGluZ0NvbXBsZXRlUmVmOiBSZWFjdC5NdXRhYmxlUmVmT2JqZWN0PE9uTG9hZGluZ0NvbXBsZXRlIHwgdW5kZWZpbmVkPlxuICBzZXRCbHVyQ29tcGxldGU6IChiOiBib29sZWFuKSA9PiB2b2lkXG4gIHNldFNob3dBbHRUZXh0OiAoYjogYm9vbGVhbikgPT4gdm9pZFxuICBzaXplc0lucHV0OiBzdHJpbmcgfCB1bmRlZmluZWRcbn1cblxuLy8gU2VlIGh0dHBzOi8vc3RhY2tvdmVyZmxvdy5jb20vcS8zOTc3NzgzMy8yNjY1MzUgZm9yIHdoeSB3ZSB1c2UgdGhpcyByZWZcbi8vIGhhbmRsZXIgaW5zdGVhZCBvZiB0aGUgaW1nJ3Mgb25Mb2FkIGF0dHJpYnV0ZS5cbmZ1bmN0aW9uIGhhbmRsZUxvYWRpbmcoXG4gIGltZzogSW1nRWxlbWVudFdpdGhEYXRhUHJvcCxcbiAgcGxhY2Vob2xkZXI6IFBsYWNlaG9sZGVyVmFsdWUsXG4gIG9uTG9hZFJlZjogUmVhY3QuTXV0YWJsZVJlZk9iamVjdDxPbkxvYWQgfCB1bmRlZmluZWQ+LFxuICBvbkxvYWRpbmdDb21wbGV0ZVJlZjogUmVhY3QuTXV0YWJsZVJlZk9iamVjdDxPbkxvYWRpbmdDb21wbGV0ZSB8IHVuZGVmaW5lZD4sXG4gIHNldEJsdXJDb21wbGV0ZTogKGI6IGJvb2xlYW4pID0+IHZvaWQsXG4gIHVub3B0aW1pemVkOiBib29sZWFuLFxuICBzaXplc0lucHV0OiBzdHJpbmcgfCB1bmRlZmluZWRcbikge1xuICBjb25zdCBzcmMgPSBpbWc/LnNyY1xuICBpZiAoIWltZyB8fCBpbWdbJ2RhdGEtbG9hZGVkLXNyYyddID09PSBzcmMpIHtcbiAgICByZXR1cm5cbiAgfVxuICBpbWdbJ2RhdGEtbG9hZGVkLXNyYyddID0gc3JjXG4gIGNvbnN0IHAgPSAnZGVjb2RlJyBpbiBpbWcgPyBpbWcuZGVjb2RlKCkgOiBQcm9taXNlLnJlc29sdmUoKVxuICBwLmNhdGNoKCgpID0+IHt9KS50aGVuKCgpID0+IHtcbiAgICBpZiAoIWltZy5wYXJlbnRFbGVtZW50IHx8ICFpbWcuaXNDb25uZWN0ZWQpIHtcbiAgICAgIC8vIEV4aXQgZWFybHkgaW4gY2FzZSBvZiByYWNlIGNvbmRpdGlvbjpcbiAgICAgIC8vIC0gb25sb2FkKCkgaXMgY2FsbGVkXG4gICAgICAvLyAtIGRlY29kZSgpIGlzIGNhbGxlZCBidXQgaW5jb21wbGV0ZVxuICAgICAgLy8gLSB1bm1vdW50IGlzIGNhbGxlZFxuICAgICAgLy8gLSBkZWNvZGUoKSBjb21wbGV0ZXNcbiAgICAgIHJldHVyblxuICAgIH1cbiAgICBpZiAocGxhY2Vob2xkZXIgIT09ICdlbXB0eScpIHtcbiAgICAgIHNldEJsdXJDb21wbGV0ZSh0cnVlKVxuICAgIH1cbiAgICBpZiAob25Mb2FkUmVmPy5jdXJyZW50KSB7XG4gICAgICAvLyBTaW5jZSB3ZSBkb24ndCBoYXZlIHRoZSBTeW50aGV0aWNFdmVudCBoZXJlLFxuICAgICAgLy8gd2UgbXVzdCBjcmVhdGUgb25lIHdpdGggdGhlIHNhbWUgc2hhcGUuXG4gICAgICAvLyBTZWUgaHR0cHM6Ly9yZWFjdGpzLm9yZy9kb2NzL2V2ZW50cy5odG1sXG4gICAgICBjb25zdCBldmVudCA9IG5ldyBFdmVudCgnbG9hZCcpXG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZXZlbnQsICd0YXJnZXQnLCB7IHdyaXRhYmxlOiBmYWxzZSwgdmFsdWU6IGltZyB9KVxuICAgICAgbGV0IHByZXZlbnRlZCA9IGZhbHNlXG4gICAgICBsZXQgc3RvcHBlZCA9IGZhbHNlXG4gICAgICBvbkxvYWRSZWYuY3VycmVudCh7XG4gICAgICAgIC4uLmV2ZW50LFxuICAgICAgICBuYXRpdmVFdmVudDogZXZlbnQsXG4gICAgICAgIGN1cnJlbnRUYXJnZXQ6IGltZyxcbiAgICAgICAgdGFyZ2V0OiBpbWcsXG4gICAgICAgIGlzRGVmYXVsdFByZXZlbnRlZDogKCkgPT4gcHJldmVudGVkLFxuICAgICAgICBpc1Byb3BhZ2F0aW9uU3RvcHBlZDogKCkgPT4gc3RvcHBlZCxcbiAgICAgICAgcGVyc2lzdDogKCkgPT4ge30sXG4gICAgICAgIHByZXZlbnREZWZhdWx0OiAoKSA9PiB7XG4gICAgICAgICAgcHJldmVudGVkID0gdHJ1ZVxuICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KClcbiAgICAgICAgfSxcbiAgICAgICAgc3RvcFByb3BhZ2F0aW9uOiAoKSA9PiB7XG4gICAgICAgICAgc3RvcHBlZCA9IHRydWVcbiAgICAgICAgICBldmVudC5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICB9LFxuICAgICAgfSlcbiAgICB9XG4gICAgaWYgKG9uTG9hZGluZ0NvbXBsZXRlUmVmPy5jdXJyZW50KSB7XG4gICAgICBvbkxvYWRpbmdDb21wbGV0ZVJlZi5jdXJyZW50KGltZylcbiAgICB9XG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICAgIGNvbnN0IG9yaWdTcmMgPSBuZXcgVVJMKHNyYywgJ2h0dHA6Ly9uJykuc2VhcmNoUGFyYW1zLmdldCgndXJsJykgfHwgc3JjXG4gICAgICBpZiAoaW1nLmdldEF0dHJpYnV0ZSgnZGF0YS1uaW1nJykgPT09ICdmaWxsJykge1xuICAgICAgICBpZiAoIXVub3B0aW1pemVkICYmICghc2l6ZXNJbnB1dCB8fCBzaXplc0lucHV0ID09PSAnMTAwdncnKSkge1xuICAgICAgICAgIGxldCB3aWR0aFZpZXdwb3J0UmF0aW8gPVxuICAgICAgICAgICAgaW1nLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpLndpZHRoIC8gd2luZG93LmlubmVyV2lkdGhcbiAgICAgICAgICBpZiAod2lkdGhWaWV3cG9ydFJhdGlvIDwgMC42KSB7XG4gICAgICAgICAgICBpZiAoc2l6ZXNJbnB1dCA9PT0gJzEwMHZ3Jykge1xuICAgICAgICAgICAgICB3YXJuT25jZShcbiAgICAgICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke29yaWdTcmN9XCIgaGFzIFwiZmlsbFwiIHByb3AgYW5kIFwic2l6ZXNcIiBwcm9wIG9mIFwiMTAwdndcIiwgYnV0IGltYWdlIGlzIG5vdCByZW5kZXJlZCBhdCBmdWxsIHZpZXdwb3J0IHdpZHRoLiBQbGVhc2UgYWRqdXN0IFwic2l6ZXNcIiB0byBpbXByb3ZlIHBhZ2UgcGVyZm9ybWFuY2UuIFJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBpLXJlZmVyZW5jZS9uZXh0L2ltYWdlI3NpemVzYFxuICAgICAgICAgICAgICApXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICB3YXJuT25jZShcbiAgICAgICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke29yaWdTcmN9XCIgaGFzIFwiZmlsbFwiIGJ1dCBpcyBtaXNzaW5nIFwic2l6ZXNcIiBwcm9wLiBQbGVhc2UgYWRkIGl0IHRvIGltcHJvdmUgcGFnZSBwZXJmb3JtYW5jZS4gUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcGktcmVmZXJlbmNlL25leHQvaW1hZ2Ujc2l6ZXNgXG4gICAgICAgICAgICAgIClcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGltZy5wYXJlbnRFbGVtZW50KSB7XG4gICAgICAgICAgY29uc3QgeyBwb3NpdGlvbiB9ID0gd2luZG93LmdldENvbXB1dGVkU3R5bGUoaW1nLnBhcmVudEVsZW1lbnQpXG4gICAgICAgICAgY29uc3QgdmFsaWQgPSBbJ2Fic29sdXRlJywgJ2ZpeGVkJywgJ3JlbGF0aXZlJ11cbiAgICAgICAgICBpZiAoIXZhbGlkLmluY2x1ZGVzKHBvc2l0aW9uKSkge1xuICAgICAgICAgICAgd2Fybk9uY2UoXG4gICAgICAgICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7b3JpZ1NyY31cIiBoYXMgXCJmaWxsXCIgYW5kIHBhcmVudCBlbGVtZW50IHdpdGggaW52YWxpZCBcInBvc2l0aW9uXCIuIFByb3ZpZGVkIFwiJHtwb3NpdGlvbn1cIiBzaG91bGQgYmUgb25lIG9mICR7dmFsaWRcbiAgICAgICAgICAgICAgICAubWFwKFN0cmluZylcbiAgICAgICAgICAgICAgICAuam9pbignLCcpfS5gXG4gICAgICAgICAgICApXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChpbWcuaGVpZ2h0ID09PSAwKSB7XG4gICAgICAgICAgd2Fybk9uY2UoXG4gICAgICAgICAgICBgSW1hZ2Ugd2l0aCBzcmMgXCIke29yaWdTcmN9XCIgaGFzIFwiZmlsbFwiIGFuZCBhIGhlaWdodCB2YWx1ZSBvZiAwLiBUaGlzIGlzIGxpa2VseSBiZWNhdXNlIHRoZSBwYXJlbnQgZWxlbWVudCBvZiB0aGUgaW1hZ2UgaGFzIG5vdCBiZWVuIHN0eWxlZCB0byBoYXZlIGEgc2V0IGhlaWdodC5gXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGhlaWdodE1vZGlmaWVkID1cbiAgICAgICAgaW1nLmhlaWdodC50b1N0cmluZygpICE9PSBpbWcuZ2V0QXR0cmlidXRlKCdoZWlnaHQnKVxuICAgICAgY29uc3Qgd2lkdGhNb2RpZmllZCA9IGltZy53aWR0aC50b1N0cmluZygpICE9PSBpbWcuZ2V0QXR0cmlidXRlKCd3aWR0aCcpXG4gICAgICBpZiAoXG4gICAgICAgIChoZWlnaHRNb2RpZmllZCAmJiAhd2lkdGhNb2RpZmllZCkgfHxcbiAgICAgICAgKCFoZWlnaHRNb2RpZmllZCAmJiB3aWR0aE1vZGlmaWVkKVxuICAgICAgKSB7XG4gICAgICAgIHdhcm5PbmNlKFxuICAgICAgICAgIGBJbWFnZSB3aXRoIHNyYyBcIiR7b3JpZ1NyY31cIiBoYXMgZWl0aGVyIHdpZHRoIG9yIGhlaWdodCBtb2RpZmllZCwgYnV0IG5vdCB0aGUgb3RoZXIuIElmIHlvdSB1c2UgQ1NTIHRvIGNoYW5nZSB0aGUgc2l6ZSBvZiB5b3VyIGltYWdlLCBhbHNvIGluY2x1ZGUgdGhlIHN0eWxlcyAnd2lkdGg6IFwiYXV0b1wiJyBvciAnaGVpZ2h0OiBcImF1dG9cIicgdG8gbWFpbnRhaW4gdGhlIGFzcGVjdCByYXRpby5gXG4gICAgICAgIClcbiAgICAgIH1cbiAgICB9XG4gIH0pXG59XG5cbmZ1bmN0aW9uIGdldER5bmFtaWNQcm9wcyhcbiAgZmV0Y2hQcmlvcml0eT86IHN0cmluZ1xuKTogUmVjb3JkPHN0cmluZywgc3RyaW5nIHwgdW5kZWZpbmVkPiB7XG4gIGlmIChCb29sZWFuKHVzZSkpIHtcbiAgICAvLyBJbiBSZWFjdCAxOS4wLjAgb3IgbmV3ZXIsIHdlIG11c3QgdXNlIGNhbWVsQ2FzZVxuICAgIC8vIHByb3AgdG8gYXZvaWQgXCJXYXJuaW5nOiBJbnZhbGlkIERPTSBwcm9wZXJ0eVwiLlxuICAgIC8vIFNlZSBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVhY3QvcHVsbC8yNTkyN1xuICAgIHJldHVybiB7IGZldGNoUHJpb3JpdHkgfVxuICB9XG4gIC8vIEluIFJlYWN0IDE4LjIuMCBvciBvbGRlciwgd2UgbXVzdCB1c2UgbG93ZXJjYXNlIHByb3BcbiAgLy8gdG8gYXZvaWQgXCJXYXJuaW5nOiBJbnZhbGlkIERPTSBwcm9wZXJ0eVwiLlxuICByZXR1cm4geyBmZXRjaHByaW9yaXR5OiBmZXRjaFByaW9yaXR5IH1cbn1cblxuY29uc3QgSW1hZ2VFbGVtZW50ID0gZm9yd2FyZFJlZjxIVE1MSW1hZ2VFbGVtZW50IHwgbnVsbCwgSW1hZ2VFbGVtZW50UHJvcHM+KFxuICAoXG4gICAge1xuICAgICAgc3JjLFxuICAgICAgc3JjU2V0LFxuICAgICAgc2l6ZXMsXG4gICAgICBoZWlnaHQsXG4gICAgICB3aWR0aCxcbiAgICAgIGRlY29kaW5nLFxuICAgICAgY2xhc3NOYW1lLFxuICAgICAgc3R5bGUsXG4gICAgICBmZXRjaFByaW9yaXR5LFxuICAgICAgcGxhY2Vob2xkZXIsXG4gICAgICBsb2FkaW5nLFxuICAgICAgdW5vcHRpbWl6ZWQsXG4gICAgICBmaWxsLFxuICAgICAgb25Mb2FkUmVmLFxuICAgICAgb25Mb2FkaW5nQ29tcGxldGVSZWYsXG4gICAgICBzZXRCbHVyQ29tcGxldGUsXG4gICAgICBzZXRTaG93QWx0VGV4dCxcbiAgICAgIHNpemVzSW5wdXQsXG4gICAgICBvbkxvYWQsXG4gICAgICBvbkVycm9yLFxuICAgICAgLi4ucmVzdFxuICAgIH0sXG4gICAgZm9yd2FyZGVkUmVmXG4gICkgPT4ge1xuICAgIGNvbnN0IG93blJlZiA9IHVzZUNhbGxiYWNrKFxuICAgICAgKGltZzogSW1nRWxlbWVudFdpdGhEYXRhUHJvcCB8IG51bGwpID0+IHtcbiAgICAgICAgaWYgKCFpbWcpIHtcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBpZiAob25FcnJvcikge1xuICAgICAgICAgIC8vIElmIHRoZSBpbWFnZSBoYXMgYW4gZXJyb3IgYmVmb3JlIHJlYWN0IGh5ZHJhdGVzLCB0aGVuIHRoZSBlcnJvciBpcyBsb3N0LlxuICAgICAgICAgIC8vIFRoZSB3b3JrYXJvdW5kIGlzIHRvIHdhaXQgdW50aWwgdGhlIGltYWdlIGlzIG1vdW50ZWQgd2hpY2ggaXMgYWZ0ZXIgaHlkcmF0aW9uLFxuICAgICAgICAgIC8vIHRoZW4gd2Ugc2V0IHRoZSBzcmMgYWdhaW4gdG8gdHJpZ2dlciB0aGUgZXJyb3IgaGFuZGxlciAoaWYgdGhlcmUgd2FzIGFuIGVycm9yKS5cbiAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tc2VsZi1hc3NpZ25cbiAgICAgICAgICBpbWcuc3JjID0gaW1nLnNyY1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgaWYgKCFzcmMpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEltYWdlIGlzIG1pc3NpbmcgcmVxdWlyZWQgXCJzcmNcIiBwcm9wZXJ0eTpgLCBpbWcpXG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChpbWcuZ2V0QXR0cmlidXRlKCdhbHQnKSA9PT0gbnVsbCkge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgYEltYWdlIGlzIG1pc3NpbmcgcmVxdWlyZWQgXCJhbHRcIiBwcm9wZXJ0eS4gUGxlYXNlIGFkZCBBbHRlcm5hdGl2ZSBUZXh0IHRvIGRlc2NyaWJlIHRoZSBpbWFnZSBmb3Igc2NyZWVuIHJlYWRlcnMgYW5kIHNlYXJjaCBlbmdpbmVzLmBcbiAgICAgICAgICAgIClcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGltZy5jb21wbGV0ZSkge1xuICAgICAgICAgIGhhbmRsZUxvYWRpbmcoXG4gICAgICAgICAgICBpbWcsXG4gICAgICAgICAgICBwbGFjZWhvbGRlcixcbiAgICAgICAgICAgIG9uTG9hZFJlZixcbiAgICAgICAgICAgIG9uTG9hZGluZ0NvbXBsZXRlUmVmLFxuICAgICAgICAgICAgc2V0Qmx1ckNvbXBsZXRlLFxuICAgICAgICAgICAgdW5vcHRpbWl6ZWQsXG4gICAgICAgICAgICBzaXplc0lucHV0XG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgW1xuICAgICAgICBzcmMsXG4gICAgICAgIHBsYWNlaG9sZGVyLFxuICAgICAgICBvbkxvYWRSZWYsXG4gICAgICAgIG9uTG9hZGluZ0NvbXBsZXRlUmVmLFxuICAgICAgICBzZXRCbHVyQ29tcGxldGUsXG4gICAgICAgIG9uRXJyb3IsXG4gICAgICAgIHVub3B0aW1pemVkLFxuICAgICAgICBzaXplc0lucHV0LFxuICAgICAgXVxuICAgIClcblxuICAgIGNvbnN0IHJlZiA9IHVzZU1lcmdlZFJlZihmb3J3YXJkZWRSZWYsIG93blJlZilcblxuICAgIHJldHVybiAoXG4gICAgICA8aW1nXG4gICAgICAgIHsuLi5yZXN0fVxuICAgICAgICB7Li4uZ2V0RHluYW1pY1Byb3BzKGZldGNoUHJpb3JpdHkpfVxuICAgICAgICAvLyBJdCdzIGludGVuZGVkIHRvIGtlZXAgYGxvYWRpbmdgIGJlZm9yZSBgc3JjYCBiZWNhdXNlIFJlYWN0IHVwZGF0ZXNcbiAgICAgICAgLy8gcHJvcHMgaW4gb3JkZXIgd2hpY2ggY2F1c2VzIFNhZmFyaS9GaXJlZm94IHRvIG5vdCBsYXp5IGxvYWQgcHJvcGVybHkuXG4gICAgICAgIC8vIFNlZSBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVhY3QvaXNzdWVzLzI1ODgzXG4gICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgIHdpZHRoPXt3aWR0aH1cbiAgICAgICAgaGVpZ2h0PXtoZWlnaHR9XG4gICAgICAgIGRlY29kaW5nPXtkZWNvZGluZ31cbiAgICAgICAgZGF0YS1uaW1nPXtmaWxsID8gJ2ZpbGwnIDogJzEnfVxuICAgICAgICBjbGFzc05hbWU9e2NsYXNzTmFtZX1cbiAgICAgICAgc3R5bGU9e3N0eWxlfVxuICAgICAgICAvLyBJdCdzIGludGVuZGVkIHRvIGtlZXAgYHNyY2AgdGhlIGxhc3QgYXR0cmlidXRlIGJlY2F1c2UgUmVhY3QgdXBkYXRlc1xuICAgICAgICAvLyBhdHRyaWJ1dGVzIGluIG9yZGVyLiBJZiB3ZSBrZWVwIGBzcmNgIHRoZSBmaXJzdCBvbmUsIFNhZmFyaSB3aWxsXG4gICAgICAgIC8vIGltbWVkaWF0ZWx5IHN0YXJ0IHRvIGZldGNoIGBzcmNgLCBiZWZvcmUgYHNpemVzYCBhbmQgYHNyY1NldGAgYXJlIGV2ZW5cbiAgICAgICAgLy8gdXBkYXRlZCBieSBSZWFjdC4gVGhhdCBjYXVzZXMgbXVsdGlwbGUgdW5uZWNlc3NhcnkgcmVxdWVzdHMgaWYgYHNyY1NldGBcbiAgICAgICAgLy8gYW5kIGBzaXplc2AgYXJlIGRlZmluZWQuXG4gICAgICAgIC8vIFRoaXMgYnVnIGNhbm5vdCBiZSByZXByb2R1Y2VkIGluIENocm9tZSBvciBGaXJlZm94LlxuICAgICAgICBzaXplcz17c2l6ZXN9XG4gICAgICAgIHNyY1NldD17c3JjU2V0fVxuICAgICAgICBzcmM9e3NyY31cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIG9uTG9hZD17KGV2ZW50KSA9PiB7XG4gICAgICAgICAgY29uc3QgaW1nID0gZXZlbnQuY3VycmVudFRhcmdldCBhcyBJbWdFbGVtZW50V2l0aERhdGFQcm9wXG4gICAgICAgICAgaGFuZGxlTG9hZGluZyhcbiAgICAgICAgICAgIGltZyxcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyLFxuICAgICAgICAgICAgb25Mb2FkUmVmLFxuICAgICAgICAgICAgb25Mb2FkaW5nQ29tcGxldGVSZWYsXG4gICAgICAgICAgICBzZXRCbHVyQ29tcGxldGUsXG4gICAgICAgICAgICB1bm9wdGltaXplZCxcbiAgICAgICAgICAgIHNpemVzSW5wdXRcbiAgICAgICAgICApXG4gICAgICAgIH19XG4gICAgICAgIG9uRXJyb3I9eyhldmVudCkgPT4ge1xuICAgICAgICAgIC8vIGlmIHRoZSByZWFsIGltYWdlIGZhaWxzIHRvIGxvYWQsIHRoaXMgd2lsbCBlbnN1cmUgXCJhbHRcIiBpcyB2aXNpYmxlXG4gICAgICAgICAgc2V0U2hvd0FsdFRleHQodHJ1ZSlcbiAgICAgICAgICBpZiAocGxhY2Vob2xkZXIgIT09ICdlbXB0eScpIHtcbiAgICAgICAgICAgIC8vIElmIHRoZSByZWFsIGltYWdlIGZhaWxzIHRvIGxvYWQsIHRoaXMgd2lsbCBzdGlsbCByZW1vdmUgdGhlIHBsYWNlaG9sZGVyLlxuICAgICAgICAgICAgc2V0Qmx1ckNvbXBsZXRlKHRydWUpXG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChvbkVycm9yKSB7XG4gICAgICAgICAgICBvbkVycm9yKGV2ZW50KVxuICAgICAgICAgIH1cbiAgICAgICAgfX1cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5cbmZ1bmN0aW9uIEltYWdlUHJlbG9hZCh7XG4gIGlzQXBwUm91dGVyLFxuICBpbWdBdHRyaWJ1dGVzLFxufToge1xuICBpc0FwcFJvdXRlcjogYm9vbGVhblxuICBpbWdBdHRyaWJ1dGVzOiBJbWdQcm9wc1xufSkge1xuICBjb25zdCBvcHRzID0ge1xuICAgIGFzOiAnaW1hZ2UnLFxuICAgIGltYWdlU3JjU2V0OiBpbWdBdHRyaWJ1dGVzLnNyY1NldCxcbiAgICBpbWFnZVNpemVzOiBpbWdBdHRyaWJ1dGVzLnNpemVzLFxuICAgIGNyb3NzT3JpZ2luOiBpbWdBdHRyaWJ1dGVzLmNyb3NzT3JpZ2luLFxuICAgIHJlZmVycmVyUG9saWN5OiBpbWdBdHRyaWJ1dGVzLnJlZmVycmVyUG9saWN5LFxuICAgIC4uLmdldER5bmFtaWNQcm9wcyhpbWdBdHRyaWJ1dGVzLmZldGNoUHJpb3JpdHkpLFxuICB9XG5cbiAgaWYgKGlzQXBwUm91dGVyICYmIFJlYWN0RE9NLnByZWxvYWQpIHtcbiAgICAvLyBTZWUgaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L3B1bGwvMjY5NDBcbiAgICBSZWFjdERPTS5wcmVsb2FkKFxuICAgICAgaW1nQXR0cmlidXRlcy5zcmMsXG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIFRPRE86IHVwZ3JhZGUgdG8gYEB0eXBlcy9yZWFjdC1kb21AMTguMy54YFxuICAgICAgb3B0c1xuICAgIClcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8SGVhZD5cbiAgICAgIDxsaW5rXG4gICAgICAgIGtleT17XG4gICAgICAgICAgJ19fbmltZy0nICtcbiAgICAgICAgICBpbWdBdHRyaWJ1dGVzLnNyYyArXG4gICAgICAgICAgaW1nQXR0cmlidXRlcy5zcmNTZXQgK1xuICAgICAgICAgIGltZ0F0dHJpYnV0ZXMuc2l6ZXNcbiAgICAgICAgfVxuICAgICAgICByZWw9XCJwcmVsb2FkXCJcbiAgICAgICAgLy8gTm90ZSBob3cgd2Ugb21pdCB0aGUgYGhyZWZgIGF0dHJpYnV0ZSwgYXMgaXQgd291bGQgb25seSBiZSByZWxldmFudFxuICAgICAgICAvLyBmb3IgYnJvd3NlcnMgdGhhdCBkbyBub3Qgc3VwcG9ydCBgaW1hZ2VzcmNzZXRgLCBhbmQgaW4gdGhvc2UgY2FzZXNcbiAgICAgICAgLy8gaXQgd291bGQgY2F1c2UgdGhlIGluY29ycmVjdCBpbWFnZSB0byBiZSBwcmVsb2FkZWQuXG4gICAgICAgIC8vXG4gICAgICAgIC8vIGh0dHBzOi8vaHRtbC5zcGVjLndoYXR3Zy5vcmcvbXVsdGlwYWdlL3NlbWFudGljcy5odG1sI2F0dHItbGluay1pbWFnZXNyY3NldFxuICAgICAgICBocmVmPXtpbWdBdHRyaWJ1dGVzLnNyY1NldCA/IHVuZGVmaW5lZCA6IGltZ0F0dHJpYnV0ZXMuc3JjfVxuICAgICAgICB7Li4ub3B0c31cbiAgICAgIC8+XG4gICAgPC9IZWFkPlxuICApXG59XG5cbi8qKlxuICogVGhlIGBJbWFnZWAgY29tcG9uZW50IGlzIHVzZWQgdG8gb3B0aW1pemUgaW1hZ2VzLlxuICpcbiAqIFJlYWQgbW9yZTogW05leHQuanMgZG9jczogYEltYWdlYF0oaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBwL2FwaS1yZWZlcmVuY2UvY29tcG9uZW50cy9pbWFnZSlcbiAqL1xuZXhwb3J0IGNvbnN0IEltYWdlID0gZm9yd2FyZFJlZjxIVE1MSW1hZ2VFbGVtZW50IHwgbnVsbCwgSW1hZ2VQcm9wcz4oXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgcGFnZXNSb3V0ZXIgPSB1c2VDb250ZXh0KFJvdXRlckNvbnRleHQpXG4gICAgLy8gV2UncmUgaW4gdGhlIGFwcCBkaXJlY3RvcnkgaWYgdGhlcmUgaXMgbm8gcGFnZXMgcm91dGVyLlxuICAgIGNvbnN0IGlzQXBwUm91dGVyID0gIXBhZ2VzUm91dGVyXG5cbiAgICBjb25zdCBjb25maWdDb250ZXh0ID0gdXNlQ29udGV4dChJbWFnZUNvbmZpZ0NvbnRleHQpXG4gICAgY29uc3QgY29uZmlnID0gdXNlTWVtbygoKSA9PiB7XG4gICAgICBjb25zdCBjID0gY29uZmlnRW52IHx8IGNvbmZpZ0NvbnRleHQgfHwgaW1hZ2VDb25maWdEZWZhdWx0XG4gICAgICBjb25zdCBhbGxTaXplcyA9IFsuLi5jLmRldmljZVNpemVzLCAuLi5jLmltYWdlU2l6ZXNdLnNvcnQoKGEsIGIpID0+IGEgLSBiKVxuICAgICAgY29uc3QgZGV2aWNlU2l6ZXMgPSBjLmRldmljZVNpemVzLnNvcnQoKGEsIGIpID0+IGEgLSBiKVxuICAgICAgY29uc3QgcXVhbGl0aWVzID0gYy5xdWFsaXRpZXM/LnNvcnQoKGEsIGIpID0+IGEgLSBiKVxuICAgICAgcmV0dXJuIHsgLi4uYywgYWxsU2l6ZXMsIGRldmljZVNpemVzLCBxdWFsaXRpZXMgfVxuICAgIH0sIFtjb25maWdDb250ZXh0XSlcblxuICAgIGNvbnN0IHsgb25Mb2FkLCBvbkxvYWRpbmdDb21wbGV0ZSB9ID0gcHJvcHNcbiAgICBjb25zdCBvbkxvYWRSZWYgPSB1c2VSZWYob25Mb2FkKVxuXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIG9uTG9hZFJlZi5jdXJyZW50ID0gb25Mb2FkXG4gICAgfSwgW29uTG9hZF0pXG5cbiAgICBjb25zdCBvbkxvYWRpbmdDb21wbGV0ZVJlZiA9IHVzZVJlZihvbkxvYWRpbmdDb21wbGV0ZSlcblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICBvbkxvYWRpbmdDb21wbGV0ZVJlZi5jdXJyZW50ID0gb25Mb2FkaW5nQ29tcGxldGVcbiAgICB9LCBbb25Mb2FkaW5nQ29tcGxldGVdKVxuXG4gICAgY29uc3QgW2JsdXJDb21wbGV0ZSwgc2V0Qmx1ckNvbXBsZXRlXSA9IHVzZVN0YXRlKGZhbHNlKVxuICAgIGNvbnN0IFtzaG93QWx0VGV4dCwgc2V0U2hvd0FsdFRleHRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgICBjb25zdCB7IHByb3BzOiBpbWdBdHRyaWJ1dGVzLCBtZXRhOiBpbWdNZXRhIH0gPSBnZXRJbWdQcm9wcyhwcm9wcywge1xuICAgICAgZGVmYXVsdExvYWRlcixcbiAgICAgIGltZ0NvbmY6IGNvbmZpZyxcbiAgICAgIGJsdXJDb21wbGV0ZSxcbiAgICAgIHNob3dBbHRUZXh0LFxuICAgIH0pXG5cbiAgICByZXR1cm4gKFxuICAgICAgPD5cbiAgICAgICAge1xuICAgICAgICAgIDxJbWFnZUVsZW1lbnRcbiAgICAgICAgICAgIHsuLi5pbWdBdHRyaWJ1dGVzfVxuICAgICAgICAgICAgdW5vcHRpbWl6ZWQ9e2ltZ01ldGEudW5vcHRpbWl6ZWR9XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj17aW1nTWV0YS5wbGFjZWhvbGRlcn1cbiAgICAgICAgICAgIGZpbGw9e2ltZ01ldGEuZmlsbH1cbiAgICAgICAgICAgIG9uTG9hZFJlZj17b25Mb2FkUmVmfVxuICAgICAgICAgICAgb25Mb2FkaW5nQ29tcGxldGVSZWY9e29uTG9hZGluZ0NvbXBsZXRlUmVmfVxuICAgICAgICAgICAgc2V0Qmx1ckNvbXBsZXRlPXtzZXRCbHVyQ29tcGxldGV9XG4gICAgICAgICAgICBzZXRTaG93QWx0VGV4dD17c2V0U2hvd0FsdFRleHR9XG4gICAgICAgICAgICBzaXplc0lucHV0PXtwcm9wcy5zaXplc31cbiAgICAgICAgICAgIHJlZj17Zm9yd2FyZGVkUmVmfVxuICAgICAgICAgIC8+XG4gICAgICAgIH1cbiAgICAgICAge2ltZ01ldGEucHJpb3JpdHkgPyAoXG4gICAgICAgICAgPEltYWdlUHJlbG9hZFxuICAgICAgICAgICAgaXNBcHBSb3V0ZXI9e2lzQXBwUm91dGVyfVxuICAgICAgICAgICAgaW1nQXR0cmlidXRlcz17aW1nQXR0cmlidXRlc31cbiAgICAgICAgICAvPlxuICAgICAgICApIDogbnVsbH1cbiAgICAgIDwvPlxuICAgIClcbiAgfVxuKVxuIl0sIm5hbWVzIjpbIkltYWdlIiwiY29uZmlnRW52IiwicHJvY2VzcyIsImVudiIsIl9fTkVYVF9JTUFHRV9PUFRTIiwid2luZG93IiwiZ2xvYmFsVGhpcyIsIl9fTkVYVF9JTUFHRV9JTVBPUlRFRCIsImhhbmRsZUxvYWRpbmciLCJpbWciLCJwbGFjZWhvbGRlciIsIm9uTG9hZFJlZiIsIm9uTG9hZGluZ0NvbXBsZXRlUmVmIiwic2V0Qmx1ckNvbXBsZXRlIiwidW5vcHRpbWl6ZWQiLCJzaXplc0lucHV0Iiwic3JjIiwicCIsImRlY29kZSIsIlByb21pc2UiLCJyZXNvbHZlIiwiY2F0Y2giLCJ0aGVuIiwicGFyZW50RWxlbWVudCIsImlzQ29ubmVjdGVkIiwiY3VycmVudCIsImV2ZW50IiwiRXZlbnQiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsIndyaXRhYmxlIiwidmFsdWUiLCJwcmV2ZW50ZWQiLCJzdG9wcGVkIiwibmF0aXZlRXZlbnQiLCJjdXJyZW50VGFyZ2V0IiwidGFyZ2V0IiwiaXNEZWZhdWx0UHJldmVudGVkIiwiaXNQcm9wYWdhdGlvblN0b3BwZWQiLCJwZXJzaXN0IiwicHJldmVudERlZmF1bHQiLCJzdG9wUHJvcGFnYXRpb24iLCJOT0RFX0VOViIsIm9yaWdTcmMiLCJVUkwiLCJzZWFyY2hQYXJhbXMiLCJnZXQiLCJnZXRBdHRyaWJ1dGUiLCJ3aWR0aFZpZXdwb3J0UmF0aW8iLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJ3aWR0aCIsImlubmVyV2lkdGgiLCJ3YXJuT25jZSIsInBvc2l0aW9uIiwiZ2V0Q29tcHV0ZWRTdHlsZSIsInZhbGlkIiwiaW5jbHVkZXMiLCJtYXAiLCJTdHJpbmciLCJqb2luIiwiaGVpZ2h0IiwiaGVpZ2h0TW9kaWZpZWQiLCJ0b1N0cmluZyIsIndpZHRoTW9kaWZpZWQiLCJnZXREeW5hbWljUHJvcHMiLCJmZXRjaFByaW9yaXR5IiwiQm9vbGVhbiIsInVzZSIsImZldGNocHJpb3JpdHkiLCJJbWFnZUVsZW1lbnQiLCJmb3J3YXJkUmVmIiwiZm9yd2FyZGVkUmVmIiwic3JjU2V0Iiwic2l6ZXMiLCJkZWNvZGluZyIsImNsYXNzTmFtZSIsInN0eWxlIiwibG9hZGluZyIsImZpbGwiLCJzZXRTaG93QWx0VGV4dCIsIm9uTG9hZCIsIm9uRXJyb3IiLCJyZXN0Iiwib3duUmVmIiwidXNlQ2FsbGJhY2siLCJjb25zb2xlIiwiZXJyb3IiLCJjb21wbGV0ZSIsInJlZiIsInVzZU1lcmdlZFJlZiIsImRhdGEtbmltZyIsIkltYWdlUHJlbG9hZCIsImlzQXBwUm91dGVyIiwiaW1nQXR0cmlidXRlcyIsIm9wdHMiLCJhcyIsImltYWdlU3JjU2V0IiwiaW1hZ2VTaXplcyIsImNyb3NzT3JpZ2luIiwicmVmZXJyZXJQb2xpY3kiLCJSZWFjdERPTSIsInByZWxvYWQiLCJIZWFkIiwibGluayIsInJlbCIsImhyZWYiLCJ1bmRlZmluZWQiLCJwcm9wcyIsInBhZ2VzUm91dGVyIiwidXNlQ29udGV4dCIsIlJvdXRlckNvbnRleHQiLCJjb25maWdDb250ZXh0IiwiSW1hZ2VDb25maWdDb250ZXh0IiwiY29uZmlnIiwidXNlTWVtbyIsImMiLCJpbWFnZUNvbmZpZ0RlZmF1bHQiLCJhbGxTaXplcyIsImRldmljZVNpemVzIiwic29ydCIsImEiLCJiIiwicXVhbGl0aWVzIiwib25Mb2FkaW5nQ29tcGxldGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJibHVyQ29tcGxldGUiLCJ1c2VTdGF0ZSIsInNob3dBbHRUZXh0IiwibWV0YSIsImltZ01ldGEiLCJnZXRJbWdQcm9wcyIsImRlZmF1bHRMb2FkZXIiLCJpbWdDb25mIiwicHJpb3JpdHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/image-component.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/get-img-props.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getImgProps\", ({\n    enumerable: true,\n    get: function() {\n        return getImgProps;\n    }\n}));\nconst _warnonce = __webpack_require__(/*! ./utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _imageblursvg = __webpack_require__(/*! ./image-blur-svg */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js\");\nconst _imageconfig = __webpack_require__(/*! ./image-config */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js\");\nconst VALID_LOADING_VALUES = [\n    'lazy',\n    'eager',\n    undefined\n];\n// Object-fit values that are not valid background-size values\nconst INVALID_BACKGROUND_SIZE_VALUES = [\n    '-moz-initial',\n    'fill',\n    'none',\n    'scale-down',\n    undefined\n];\nfunction isStaticRequire(src) {\n    return src.default !== undefined;\n}\nfunction isStaticImageData(src) {\n    return src.src !== undefined;\n}\nfunction isStaticImport(src) {\n    return !!src && typeof src === 'object' && (isStaticRequire(src) || isStaticImageData(src));\n}\nconst allImgs = new Map();\nlet perfObserver;\nfunction getInt(x) {\n    if (typeof x === 'undefined') {\n        return x;\n    }\n    if (typeof x === 'number') {\n        return Number.isFinite(x) ? x : NaN;\n    }\n    if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n        return parseInt(x, 10);\n    }\n    return NaN;\n}\nfunction getWidths(param, width, sizes) {\n    let { deviceSizes, allSizes } = param;\n    if (sizes) {\n        // Find all the \"vw\" percent sizes used in the sizes prop\n        const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g;\n        const percentSizes = [];\n        for(let match; match = viewportWidthRe.exec(sizes); match){\n            percentSizes.push(parseInt(match[2]));\n        }\n        if (percentSizes.length) {\n            const smallestRatio = Math.min(...percentSizes) * 0.01;\n            return {\n                widths: allSizes.filter((s)=>s >= deviceSizes[0] * smallestRatio),\n                kind: 'w'\n            };\n        }\n        return {\n            widths: allSizes,\n            kind: 'w'\n        };\n    }\n    if (typeof width !== 'number') {\n        return {\n            widths: deviceSizes,\n            kind: 'w'\n        };\n    }\n    const widths = [\n        ...new Set(// > are actually 3x in the green color, but only 1.5x in the red and\n        // > blue colors. Showing a 3x resolution image in the app vs a 2x\n        // > resolution image will be visually the same, though the 3x image\n        // > takes significantly more data. Even true 3x resolution screens are\n        // > wasteful as the human eye cannot see that level of detail without\n        // > something like a magnifying glass.\n        // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n        [\n            width,\n            width * 2 /*, width * 3*/ \n        ].map((w)=>allSizes.find((p)=>p >= w) || allSizes[allSizes.length - 1]))\n    ];\n    return {\n        widths,\n        kind: 'x'\n    };\n}\nfunction generateImgAttrs(param) {\n    let { config, src, unoptimized, width, quality, sizes, loader } = param;\n    if (unoptimized) {\n        return {\n            src,\n            srcSet: undefined,\n            sizes: undefined\n        };\n    }\n    const { widths, kind } = getWidths(config, width, sizes);\n    const last = widths.length - 1;\n    return {\n        sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n        srcSet: widths.map((w, i)=>loader({\n                config,\n                src,\n                quality,\n                width: w\n            }) + \" \" + (kind === 'w' ? w : i + 1) + kind).join(', '),\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        src: loader({\n            config,\n            src,\n            quality,\n            width: widths[last]\n        })\n    };\n}\nfunction getImgProps(param, _state) {\n    let { src, sizes, unoptimized = false, priority = false, loading, className, quality, width, height, fill = false, style, overrideSrc, onLoad, onLoadingComplete, placeholder = 'empty', blurDataURL, fetchPriority, decoding = 'async', layout, objectFit, objectPosition, lazyBoundary, lazyRoot, ...rest } = param;\n    const { imgConf, showAltText, blurComplete, defaultLoader } = _state;\n    let config;\n    let c = imgConf || _imageconfig.imageConfigDefault;\n    if ('allSizes' in c) {\n        config = c;\n    } else {\n        var _c_qualities;\n        const allSizes = [\n            ...c.deviceSizes,\n            ...c.imageSizes\n        ].sort((a, b)=>a - b);\n        const deviceSizes = c.deviceSizes.sort((a, b)=>a - b);\n        const qualities = (_c_qualities = c.qualities) == null ? void 0 : _c_qualities.sort((a, b)=>a - b);\n        config = {\n            ...c,\n            allSizes,\n            deviceSizes,\n            qualities\n        };\n    }\n    if (typeof defaultLoader === 'undefined') {\n        throw Object.defineProperty(new Error('images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'), \"__NEXT_ERROR_CODE\", {\n            value: \"E163\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    let loader = rest.loader || defaultLoader;\n    // Remove property so it's not spread on <img> element\n    delete rest.loader;\n    delete rest.srcSet;\n    // This special value indicates that the user\n    // didn't define a \"loader\" prop or \"loader\" config.\n    const isDefaultLoader = '__next_img_default' in loader;\n    if (isDefaultLoader) {\n        if (config.loader === 'custom') {\n            throw Object.defineProperty(new Error('Image with src \"' + src + '\" is missing \"loader\" prop.' + \"\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader\"), \"__NEXT_ERROR_CODE\", {\n                value: \"E252\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    } else {\n        // The user defined a \"loader\" prop or config.\n        // Since the config object is internal only, we\n        // must not pass it to the user-defined \"loader\".\n        const customImageLoader = loader;\n        loader = (obj)=>{\n            const { config: _, ...opts } = obj;\n            return customImageLoader(opts);\n        };\n    }\n    if (layout) {\n        if (layout === 'fill') {\n            fill = true;\n        }\n        const layoutToStyle = {\n            intrinsic: {\n                maxWidth: '100%',\n                height: 'auto'\n            },\n            responsive: {\n                width: '100%',\n                height: 'auto'\n            }\n        };\n        const layoutToSizes = {\n            responsive: '100vw',\n            fill: '100vw'\n        };\n        const layoutStyle = layoutToStyle[layout];\n        if (layoutStyle) {\n            style = {\n                ...style,\n                ...layoutStyle\n            };\n        }\n        const layoutSizes = layoutToSizes[layout];\n        if (layoutSizes && !sizes) {\n            sizes = layoutSizes;\n        }\n    }\n    let staticSrc = '';\n    let widthInt = getInt(width);\n    let heightInt = getInt(height);\n    let blurWidth;\n    let blurHeight;\n    if (isStaticImport(src)) {\n        const staticImageData = isStaticRequire(src) ? src.default : src;\n        if (!staticImageData.src) {\n            throw Object.defineProperty(new Error(\"An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received \" + JSON.stringify(staticImageData)), \"__NEXT_ERROR_CODE\", {\n                value: \"E460\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (!staticImageData.height || !staticImageData.width) {\n            throw Object.defineProperty(new Error(\"An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received \" + JSON.stringify(staticImageData)), \"__NEXT_ERROR_CODE\", {\n                value: \"E48\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        blurWidth = staticImageData.blurWidth;\n        blurHeight = staticImageData.blurHeight;\n        blurDataURL = blurDataURL || staticImageData.blurDataURL;\n        staticSrc = staticImageData.src;\n        if (!fill) {\n            if (!widthInt && !heightInt) {\n                widthInt = staticImageData.width;\n                heightInt = staticImageData.height;\n            } else if (widthInt && !heightInt) {\n                const ratio = widthInt / staticImageData.width;\n                heightInt = Math.round(staticImageData.height * ratio);\n            } else if (!widthInt && heightInt) {\n                const ratio = heightInt / staticImageData.height;\n                widthInt = Math.round(staticImageData.width * ratio);\n            }\n        }\n    }\n    src = typeof src === 'string' ? src : staticSrc;\n    let isLazy = !priority && (loading === 'lazy' || typeof loading === 'undefined');\n    if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n        // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n        unoptimized = true;\n        isLazy = false;\n    }\n    if (config.unoptimized) {\n        unoptimized = true;\n    }\n    if (isDefaultLoader && !config.dangerouslyAllowSVG && src.split('?', 1)[0].endsWith('.svg')) {\n        // Special case to make svg serve as-is to avoid proxying\n        // through the built-in Image Optimization API.\n        unoptimized = true;\n    }\n    const qualityInt = getInt(quality);\n    if (true) {\n        if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n            throw Object.defineProperty(new Error(\"Image Optimization using the default loader is not compatible with `{ output: 'export' }`.\\n  Possible solutions:\\n    - Remove `{ output: 'export' }` and run \\\"next start\\\" to run server mode including the Image Optimization API.\\n    - Configure `{ images: { unoptimized: true } }` in `next.config.js` to disable the Image Optimization API.\\n  Read more: https://nextjs.org/docs/messages/export-image-api\"), \"__NEXT_ERROR_CODE\", {\n                value: \"E500\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (!src) {\n            // React doesn't show the stack trace and there's\n            // no `src` to help identify which image, so we\n            // instead console.error(ref) during mount.\n            unoptimized = true;\n        } else {\n            if (fill) {\n                if (width) {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" has both \"width\" and \"fill\" properties. Only one should be used.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E96\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (height) {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" has both \"height\" and \"fill\" properties. Only one should be used.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E115\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if ((style == null ? void 0 : style.position) && style.position !== 'absolute') {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E216\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if ((style == null ? void 0 : style.width) && style.width !== '100%') {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E73\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if ((style == null ? void 0 : style.height) && style.height !== '100%') {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E404\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            } else {\n                if (typeof widthInt === 'undefined') {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" is missing required \"width\" property.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E451\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                } else if (isNaN(widthInt)) {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" has invalid \"width\" property. Expected a numeric value in pixels but received \"' + width + '\".'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E66\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (typeof heightInt === 'undefined') {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" is missing required \"height\" property.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E397\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                } else if (isNaN(heightInt)) {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" has invalid \"height\" property. Expected a numeric value in pixels but received \"' + height + '\".'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E444\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // eslint-disable-next-line no-control-regex\n                if (/^[\\x00-\\x20]/.test(src)) {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E176\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // eslint-disable-next-line no-control-regex\n                if (/[\\x00-\\x20]$/.test(src)) {\n                    throw Object.defineProperty(new Error('Image with src \"' + src + '\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E21\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n        if (!VALID_LOADING_VALUES.includes(loading)) {\n            throw Object.defineProperty(new Error('Image with src \"' + src + '\" has invalid \"loading\" property. Provided \"' + loading + '\" should be one of ' + VALID_LOADING_VALUES.map(String).join(',') + \".\"), \"__NEXT_ERROR_CODE\", {\n                value: \"E357\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (priority && loading === 'lazy') {\n            throw Object.defineProperty(new Error('Image with src \"' + src + '\" has both \"priority\" and \"loading=\\'lazy\\'\" properties. Only one should be used.'), \"__NEXT_ERROR_CODE\", {\n                value: \"E218\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (placeholder !== 'empty' && placeholder !== 'blur' && !placeholder.startsWith('data:image/')) {\n            throw Object.defineProperty(new Error('Image with src \"' + src + '\" has invalid \"placeholder\" property \"' + placeholder + '\".'), \"__NEXT_ERROR_CODE\", {\n                value: \"E431\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (placeholder !== 'empty') {\n            if (widthInt && heightInt && widthInt * heightInt < 1600) {\n                (0, _warnonce.warnOnce)('Image with src \"' + src + '\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.');\n            }\n        }\n        if (placeholder === 'blur' && !blurDataURL) {\n            const VALID_BLUR_EXT = [\n                'jpeg',\n                'png',\n                'webp',\n                'avif'\n            ] // should match next-image-loader\n            ;\n            throw Object.defineProperty(new Error('Image with src \"' + src + '\" has \"placeholder=\\'blur\\'\" property but is missing the \"blurDataURL\" property.\\n        Possible solutions:\\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\\n          - Change the \"src\" property to a static import with one of the supported file types: ' + VALID_BLUR_EXT.join(',') + ' (animated images not supported)\\n          - Remove the \"placeholder\" property, effectively no blur effect\\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url'), \"__NEXT_ERROR_CODE\", {\n                value: \"E371\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if ('ref' in rest) {\n            (0, _warnonce.warnOnce)('Image with src \"' + src + '\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.');\n        }\n        if (!unoptimized && !isDefaultLoader) {\n            const urlStr = loader({\n                config,\n                src,\n                width: widthInt || 400,\n                quality: qualityInt || 75\n            });\n            let url;\n            try {\n                url = new URL(urlStr);\n            } catch (err) {}\n            if (urlStr === src || url && url.pathname === src && !url.search) {\n                (0, _warnonce.warnOnce)('Image with src \"' + src + '\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.' + \"\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width\");\n            }\n        }\n        if (onLoadingComplete) {\n            (0, _warnonce.warnOnce)('Image with src \"' + src + '\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.');\n        }\n        for (const [legacyKey, legacyValue] of Object.entries({\n            layout,\n            objectFit,\n            objectPosition,\n            lazyBoundary,\n            lazyRoot\n        })){\n            if (legacyValue) {\n                (0, _warnonce.warnOnce)('Image with src \"' + src + '\" has legacy prop \"' + legacyKey + '\". Did you forget to run the codemod?' + \"\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13\");\n            }\n        }\n        if ( true && !perfObserver && window.PerformanceObserver) {\n            perfObserver = new PerformanceObserver((entryList)=>{\n                for (const entry of entryList.getEntries()){\n                    var _entry_element;\n                    // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n                    const imgSrc = (entry == null ? void 0 : (_entry_element = entry.element) == null ? void 0 : _entry_element.src) || '';\n                    const lcpImage = allImgs.get(imgSrc);\n                    if (lcpImage && !lcpImage.priority && lcpImage.placeholder === 'empty' && !lcpImage.src.startsWith('data:') && !lcpImage.src.startsWith('blob:')) {\n                        // https://web.dev/lcp/#measure-lcp-in-javascript\n                        (0, _warnonce.warnOnce)('Image with src \"' + lcpImage.src + '\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.' + \"\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority\");\n                    }\n                }\n            });\n            try {\n                perfObserver.observe({\n                    type: 'largest-contentful-paint',\n                    buffered: true\n                });\n            } catch (err) {\n                // Log error but don't crash the app\n                console.error(err);\n            }\n        }\n    }\n    const imgStyle = Object.assign(fill ? {\n        position: 'absolute',\n        height: '100%',\n        width: '100%',\n        left: 0,\n        top: 0,\n        right: 0,\n        bottom: 0,\n        objectFit,\n        objectPosition\n    } : {}, showAltText ? {} : {\n        color: 'transparent'\n    }, style);\n    const backgroundImage = !blurComplete && placeholder !== 'empty' ? placeholder === 'blur' ? 'url(\"data:image/svg+xml;charset=utf-8,' + (0, _imageblursvg.getImageBlurSvg)({\n        widthInt,\n        heightInt,\n        blurWidth,\n        blurHeight,\n        blurDataURL: blurDataURL || '',\n        objectFit: imgStyle.objectFit\n    }) + '\")' : 'url(\"' + placeholder + '\")' // assume `data:image/`\n     : null;\n    const backgroundSize = !INVALID_BACKGROUND_SIZE_VALUES.includes(imgStyle.objectFit) ? imgStyle.objectFit : imgStyle.objectFit === 'fill' ? '100% 100%' // the background-size equivalent of `fill`\n     : 'cover';\n    let placeholderStyle = backgroundImage ? {\n        backgroundSize,\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage\n    } : {};\n    if (true) {\n        if (placeholderStyle.backgroundImage && placeholder === 'blur' && (blurDataURL == null ? void 0 : blurDataURL.startsWith('/'))) {\n            // During `next dev`, we don't want to generate blur placeholders with webpack\n            // because it can delay starting the dev server. Instead, `next-image-loader.js`\n            // will inline a special url to lazily generate the blur placeholder at request time.\n            placeholderStyle.backgroundImage = 'url(\"' + blurDataURL + '\")';\n        }\n    }\n    const imgAttributes = generateImgAttrs({\n        config,\n        src,\n        unoptimized,\n        width: widthInt,\n        quality: qualityInt,\n        sizes,\n        loader\n    });\n    if (true) {\n        if (true) {\n            let fullUrl;\n            try {\n                fullUrl = new URL(imgAttributes.src);\n            } catch (e) {\n                fullUrl = new URL(imgAttributes.src, window.location.href);\n            }\n            allImgs.set(fullUrl.href, {\n                src,\n                priority,\n                placeholder\n            });\n        }\n    }\n    const props = {\n        ...rest,\n        loading: isLazy ? 'lazy' : loading,\n        fetchPriority,\n        width: widthInt,\n        height: heightInt,\n        decoding,\n        className,\n        style: {\n            ...imgStyle,\n            ...placeholderStyle\n        },\n        sizes: imgAttributes.sizes,\n        srcSet: imgAttributes.srcSet,\n        src: overrideSrc || imgAttributes.src\n    };\n    const meta = {\n        unoptimized,\n        priority,\n        placeholder,\n        fill\n    };\n    return {\n        props,\n        meta\n    };\n} //# sourceMappingURL=get-img-props.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ImageConfigContext\", ({\n    enumerable: true,\n    get: function() {\n        return ImageConfigContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _imageconfig = __webpack_require__(/*! ./image-config */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js\");\nconst ImageConfigContext = _react.default.createContext(_imageconfig.imageConfigDefault);\nif (true) {\n    ImageConfigContext.displayName = 'ImageConfigContext';\n} //# sourceMappingURL=image-config-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9pbWFnZS1jb25maWctY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O3NEQUlhQTs7O2VBQUFBOzs7OzRFQUpLO3lDQUVpQjtBQUU1QixNQUFNQSxxQkFDWEMsT0FBQUEsT0FBSyxDQUFDQyxhQUFhLENBQXNCQyxhQUFBQSxrQkFBa0I7QUFFN0QsSUFBSUMsSUFBb0IsRUFBbUI7SUFDekNKLG1CQUFtQk8sV0FBVyxHQUFHO0FBQ25DIiwic291cmNlcyI6WyJDOlxcc3JjXFxzaGFyZWRcXGxpYlxcaW1hZ2UtY29uZmlnLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHR5cGUgeyBJbWFnZUNvbmZpZ0NvbXBsZXRlIH0gZnJvbSAnLi9pbWFnZS1jb25maWcnXG5pbXBvcnQgeyBpbWFnZUNvbmZpZ0RlZmF1bHQgfSBmcm9tICcuL2ltYWdlLWNvbmZpZydcblxuZXhwb3J0IGNvbnN0IEltYWdlQ29uZmlnQ29udGV4dCA9XG4gIFJlYWN0LmNyZWF0ZUNvbnRleHQ8SW1hZ2VDb25maWdDb21wbGV0ZT4oaW1hZ2VDb25maWdEZWZhdWx0KVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBJbWFnZUNvbmZpZ0NvbnRleHQuZGlzcGxheU5hbWUgPSAnSW1hZ2VDb25maWdDb250ZXh0J1xufVxuIl0sIm5hbWVzIjpbIkltYWdlQ29uZmlnQ29udGV4dCIsIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsImltYWdlQ29uZmlnRGVmYXVsdCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/image-loader.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst DEFAULT_Q = 75;\nfunction defaultLoader(param) {\n    let { config, src, width, quality } = param;\n    var _config_qualities;\n    if (true) {\n        const missingValues = [];\n        // these should always be provided but make sure they are\n        if (!src) missingValues.push('src');\n        if (!width) missingValues.push('width');\n        if (missingValues.length > 0) {\n            throw Object.defineProperty(new Error(\"Next Image Optimization requires \" + missingValues.join(', ') + \" to be provided. Make sure you pass them as props to the `next/image` component. Received: \" + JSON.stringify({\n                src,\n                width,\n                quality\n            })), \"__NEXT_ERROR_CODE\", {\n                value: \"E188\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (src.startsWith('//')) {\n            throw Object.defineProperty(new Error('Failed to parse src \"' + src + '\" on `next/image`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)'), \"__NEXT_ERROR_CODE\", {\n                value: \"E360\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (src.startsWith('/') && config.localPatterns) {\n            if (true) {\n                // We use dynamic require because this should only error in development\n                const { hasLocalMatch } = __webpack_require__(/*! ./match-local-pattern */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js\");\n                if (!hasLocalMatch(config.localPatterns, src)) {\n                    throw Object.defineProperty(new Error(\"Invalid src prop (\" + src + \") on `next/image` does not match `images.localPatterns` configured in your `next.config.js`\\n\" + \"See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E426\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n        if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n            let parsedSrc;\n            try {\n                parsedSrc = new URL(src);\n            } catch (err) {\n                console.error(err);\n                throw Object.defineProperty(new Error('Failed to parse src \"' + src + '\" on `next/image`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E63\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (true) {\n                // We use dynamic require because this should only error in development\n                const { hasRemoteMatch } = __webpack_require__(/*! ./match-remote-pattern */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js\");\n                if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n                    throw Object.defineProperty(new Error(\"Invalid src prop (\" + src + ') on `next/image`, hostname \"' + parsedSrc.hostname + '\" is not configured under images in your `next.config.js`\\n' + \"See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E231\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n        if (quality && config.qualities && !config.qualities.includes(quality)) {\n            throw Object.defineProperty(new Error(\"Invalid quality prop (\" + quality + \") on `next/image` does not match `images.qualities` configured in your `next.config.js`\\n\" + \"See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities\"), \"__NEXT_ERROR_CODE\", {\n                value: \"E623\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    const q = quality || ((_config_qualities = config.qualities) == null ? void 0 : _config_qualities.reduce((prev, cur)=>Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev)) || DEFAULT_Q;\n    return config.path + \"?url=\" + encodeURIComponent(src) + \"&w=\" + width + \"&q=\" + q + (src.startsWith('/_next/static/media/') && false ? 0 : '');\n}\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true;\nconst _default = defaultLoader; //# sourceMappingURL=image-loader.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvZm9ybWF0LXVybC5qcyIsIm1hcHBpbmdzIjoiQUFBQSx1Q0FBdUM7QUFDdkMsc0RBQXNEO0FBQ3RELEVBQUU7QUFDRiwwRUFBMEU7QUFDMUUsZ0VBQWdFO0FBQ2hFLHNFQUFzRTtBQUN0RSxzRUFBc0U7QUFDdEUsNEVBQTRFO0FBQzVFLHFFQUFxRTtBQUNyRSx3QkFBd0I7QUFDeEIsRUFBRTtBQUNGLDBFQUEwRTtBQUMxRSx5REFBeUQ7QUFDekQsRUFBRTtBQUNGLDBFQUEwRTtBQUMxRSw2REFBNkQ7QUFDN0QsNEVBQTRFO0FBQzVFLDJFQUEyRTtBQUMzRSx3RUFBd0U7QUFDeEUsNEVBQTRFO0FBQzVFLHlDQUF5Qzs7Ozs7Ozs7Ozs7OztJQVF6QkEsU0FBUztlQUFUQTs7SUE2REFDLG9CQUFvQjtlQUFwQkE7O0lBZkhDLGFBQWE7ZUFBYkE7Ozs7bUZBbERnQjtBQUU3QixNQUFNQyxtQkFBbUI7QUFFbEIsU0FBU0gsVUFBVUksTUFBaUI7SUFDekMsSUFBSSxFQUFFQyxJQUFJLEVBQUVDLFFBQVEsRUFBRSxHQUFHRjtJQUN6QixJQUFJRyxXQUFXSCxPQUFPRyxRQUFRLElBQUk7SUFDbEMsSUFBSUMsV0FBV0osT0FBT0ksUUFBUSxJQUFJO0lBQ2xDLElBQUlDLE9BQU9MLE9BQU9LLElBQUksSUFBSTtJQUMxQixJQUFJQyxRQUFRTixPQUFPTSxLQUFLLElBQUk7SUFDNUIsSUFBSUMsT0FBdUI7SUFFM0JOLE9BQU9BLE9BQU9PLG1CQUFtQlAsTUFBTVEsT0FBTyxDQUFDLFFBQVEsT0FBTyxNQUFNO0lBRXBFLElBQUlULE9BQU9PLElBQUksRUFBRTtRQUNmQSxPQUFPTixPQUFPRCxPQUFPTyxJQUFJO0lBQzNCLE9BQU8sSUFBSUwsVUFBVTtRQUNuQkssT0FBT04sT0FBUSxFQUFDQyxTQUFTUSxPQUFPLENBQUMsT0FBUSxNQUFHUixXQUFTLE1BQUtBLFFBQUFBLENBQU87UUFDakUsSUFBSUYsT0FBT1csSUFBSSxFQUFFO1lBQ2ZKLFFBQVEsTUFBTVAsT0FBT1csSUFBSTtRQUMzQjtJQUNGO0lBRUEsSUFBSUwsU0FBUyxPQUFPQSxVQUFVLFVBQVU7UUFDdENBLFFBQVFNLE9BQU9DLGFBQVlDLHNCQUFzQixDQUFDUjtJQUNwRDtJQUVBLElBQUlTLFNBQVNmLE9BQU9lLE1BQU0sSUFBS1QsU0FBVSxNQUFHQSxTQUFZO0lBRXhELElBQUlILFlBQVksQ0FBQ0EsU0FBU2EsUUFBUSxDQUFDLE1BQU1iLFlBQVk7SUFFckQsSUFDRUgsT0FBT2lCLE9BQU8sSUFDWixFQUFDZCxZQUFZSixpQkFBaUJtQixJQUFJLENBQUNmLFNBQUFBLENBQVEsSUFBTUksU0FBUyxPQUM1RDtRQUNBQSxPQUFPLE9BQVFBLENBQUFBLFFBQVEsR0FBQztRQUN4QixJQUFJSCxZQUFZQSxRQUFRLENBQUMsRUFBRSxLQUFLLEtBQUtBLFdBQVcsTUFBTUE7SUFDeEQsT0FBTyxJQUFJLENBQUNHLE1BQU07UUFDaEJBLE9BQU87SUFDVDtJQUVBLElBQUlGLFFBQVFBLElBQUksQ0FBQyxFQUFFLEtBQUssS0FBS0EsT0FBTyxNQUFNQTtJQUMxQyxJQUFJVSxVQUFVQSxNQUFNLENBQUMsRUFBRSxLQUFLLEtBQUtBLFNBQVMsTUFBTUE7SUFFaERYLFdBQVdBLFNBQVNLLE9BQU8sQ0FBQyxTQUFTRDtJQUNyQ08sU0FBU0EsT0FBT04sT0FBTyxDQUFDLEtBQUs7SUFFN0IsT0FBUSxLQUFFTixXQUFXSSxPQUFPSCxXQUFXVyxTQUFTVjtBQUNsRDtBQUVPLE1BQU1QLGdCQUFnQjtJQUMzQjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVNLFNBQVNELHFCQUFxQnNCLEdBQWM7SUFDakQsSUFBSUMsSUFBb0IsRUFBb0I7UUFDMUMsSUFBSUQsUUFBUSxRQUFRLE9BQU9BLFFBQVEsVUFBVTtZQUMzQ0ksT0FBT0MsSUFBSSxDQUFDTCxLQUFLTSxPQUFPLENBQUMsQ0FBQ0M7Z0JBQ3hCLElBQUksQ0FBQzVCLGNBQWM2QixRQUFRLENBQUNELE1BQU07b0JBQ2hDRSxRQUFRQyxJQUFJLENBQ1QsdURBQW9ESDtnQkFFekQ7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxPQUFPOUIsVUFBVXVCO0FBQ25CIiwic291cmNlcyI6WyJDOlxcc3JjXFxzaGFyZWRcXGxpYlxccm91dGVyXFx1dGlsc1xcZm9ybWF0LXVybC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGb3JtYXQgZnVuY3Rpb24gbW9kaWZpZWQgZnJvbSBub2RlanNcbi8vIENvcHlyaWdodCBKb3llbnQsIEluYy4gYW5kIG90aGVyIE5vZGUgY29udHJpYnV0b3JzLlxuLy9cbi8vIFBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhXG4vLyBjb3B5IG9mIHRoaXMgc29mdHdhcmUgYW5kIGFzc29jaWF0ZWQgZG9jdW1lbnRhdGlvbiBmaWxlcyAodGhlXG4vLyBcIlNvZnR3YXJlXCIpLCB0byBkZWFsIGluIHRoZSBTb2Z0d2FyZSB3aXRob3V0IHJlc3RyaWN0aW9uLCBpbmNsdWRpbmdcbi8vIHdpdGhvdXQgbGltaXRhdGlvbiB0aGUgcmlnaHRzIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBtZXJnZSwgcHVibGlzaCxcbi8vIGRpc3RyaWJ1dGUsIHN1YmxpY2Vuc2UsIGFuZC9vciBzZWxsIGNvcGllcyBvZiB0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXRcbi8vIHBlcnNvbnMgdG8gd2hvbSB0aGUgU29mdHdhcmUgaXMgZnVybmlzaGVkIHRvIGRvIHNvLCBzdWJqZWN0IHRvIHRoZVxuLy8gZm9sbG93aW5nIGNvbmRpdGlvbnM6XG4vL1xuLy8gVGhlIGFib3ZlIGNvcHlyaWdodCBub3RpY2UgYW5kIHRoaXMgcGVybWlzc2lvbiBub3RpY2Ugc2hhbGwgYmUgaW5jbHVkZWRcbi8vIGluIGFsbCBjb3BpZXMgb3Igc3Vic3RhbnRpYWwgcG9ydGlvbnMgb2YgdGhlIFNvZnR3YXJlLlxuLy9cbi8vIFRIRSBTT0ZUV0FSRSBJUyBQUk9WSURFRCBcIkFTIElTXCIsIFdJVEhPVVQgV0FSUkFOVFkgT0YgQU5ZIEtJTkQsIEVYUFJFU1Ncbi8vIE9SIElNUExJRUQsIElOQ0xVRElORyBCVVQgTk9UIExJTUlURUQgVE8gVEhFIFdBUlJBTlRJRVMgT0Zcbi8vIE1FUkNIQU5UQUJJTElUWSwgRklUTkVTUyBGT1IgQSBQQVJUSUNVTEFSIFBVUlBPU0UgQU5EIE5PTklORlJJTkdFTUVOVC4gSU5cbi8vIE5PIEVWRU5UIFNIQUxMIFRIRSBBVVRIT1JTIE9SIENPUFlSSUdIVCBIT0xERVJTIEJFIExJQUJMRSBGT1IgQU5ZIENMQUlNLFxuLy8gREFNQUdFUyBPUiBPVEhFUiBMSUFCSUxJVFksIFdIRVRIRVIgSU4gQU4gQUNUSU9OIE9GIENPTlRSQUNULCBUT1JUIE9SXG4vLyBPVEhFUldJU0UsIEFSSVNJTkcgRlJPTSwgT1VUIE9GIE9SIElOIENPTk5FQ1RJT04gV0lUSCBUSEUgU09GVFdBUkUgT1IgVEhFXG4vLyBVU0UgT1IgT1RIRVIgREVBTElOR1MgSU4gVEhFIFNPRlRXQVJFLlxuXG5pbXBvcnQgdHlwZSB7IFVybE9iamVjdCB9IGZyb20gJ3VybCdcbmltcG9ydCB0eXBlIHsgUGFyc2VkVXJsUXVlcnkgfSBmcm9tICdxdWVyeXN0cmluZydcbmltcG9ydCAqIGFzIHF1ZXJ5c3RyaW5nIGZyb20gJy4vcXVlcnlzdHJpbmcnXG5cbmNvbnN0IHNsYXNoZWRQcm90b2NvbHMgPSAvaHR0cHM/fGZ0cHxnb3BoZXJ8ZmlsZS9cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdFVybCh1cmxPYmo6IFVybE9iamVjdCkge1xuICBsZXQgeyBhdXRoLCBob3N0bmFtZSB9ID0gdXJsT2JqXG4gIGxldCBwcm90b2NvbCA9IHVybE9iai5wcm90b2NvbCB8fCAnJ1xuICBsZXQgcGF0aG5hbWUgPSB1cmxPYmoucGF0aG5hbWUgfHwgJydcbiAgbGV0IGhhc2ggPSB1cmxPYmouaGFzaCB8fCAnJ1xuICBsZXQgcXVlcnkgPSB1cmxPYmoucXVlcnkgfHwgJydcbiAgbGV0IGhvc3Q6IHN0cmluZyB8IGZhbHNlID0gZmFsc2VcblxuICBhdXRoID0gYXV0aCA/IGVuY29kZVVSSUNvbXBvbmVudChhdXRoKS5yZXBsYWNlKC8lM0EvaSwgJzonKSArICdAJyA6ICcnXG5cbiAgaWYgKHVybE9iai5ob3N0KSB7XG4gICAgaG9zdCA9IGF1dGggKyB1cmxPYmouaG9zdFxuICB9IGVsc2UgaWYgKGhvc3RuYW1lKSB7XG4gICAgaG9zdCA9IGF1dGggKyAofmhvc3RuYW1lLmluZGV4T2YoJzonKSA/IGBbJHtob3N0bmFtZX1dYCA6IGhvc3RuYW1lKVxuICAgIGlmICh1cmxPYmoucG9ydCkge1xuICAgICAgaG9zdCArPSAnOicgKyB1cmxPYmoucG9ydFxuICAgIH1cbiAgfVxuXG4gIGlmIChxdWVyeSAmJiB0eXBlb2YgcXVlcnkgPT09ICdvYmplY3QnKSB7XG4gICAgcXVlcnkgPSBTdHJpbmcocXVlcnlzdHJpbmcudXJsUXVlcnlUb1NlYXJjaFBhcmFtcyhxdWVyeSBhcyBQYXJzZWRVcmxRdWVyeSkpXG4gIH1cblxuICBsZXQgc2VhcmNoID0gdXJsT2JqLnNlYXJjaCB8fCAocXVlcnkgJiYgYD8ke3F1ZXJ5fWApIHx8ICcnXG5cbiAgaWYgKHByb3RvY29sICYmICFwcm90b2NvbC5lbmRzV2l0aCgnOicpKSBwcm90b2NvbCArPSAnOidcblxuICBpZiAoXG4gICAgdXJsT2JqLnNsYXNoZXMgfHxcbiAgICAoKCFwcm90b2NvbCB8fCBzbGFzaGVkUHJvdG9jb2xzLnRlc3QocHJvdG9jb2wpKSAmJiBob3N0ICE9PSBmYWxzZSlcbiAgKSB7XG4gICAgaG9zdCA9ICcvLycgKyAoaG9zdCB8fCAnJylcbiAgICBpZiAocGF0aG5hbWUgJiYgcGF0aG5hbWVbMF0gIT09ICcvJykgcGF0aG5hbWUgPSAnLycgKyBwYXRobmFtZVxuICB9IGVsc2UgaWYgKCFob3N0KSB7XG4gICAgaG9zdCA9ICcnXG4gIH1cblxuICBpZiAoaGFzaCAmJiBoYXNoWzBdICE9PSAnIycpIGhhc2ggPSAnIycgKyBoYXNoXG4gIGlmIChzZWFyY2ggJiYgc2VhcmNoWzBdICE9PSAnPycpIHNlYXJjaCA9ICc/JyArIHNlYXJjaFxuXG4gIHBhdGhuYW1lID0gcGF0aG5hbWUucmVwbGFjZSgvWz8jXS9nLCBlbmNvZGVVUklDb21wb25lbnQpXG4gIHNlYXJjaCA9IHNlYXJjaC5yZXBsYWNlKCcjJywgJyUyMycpXG5cbiAgcmV0dXJuIGAke3Byb3RvY29sfSR7aG9zdH0ke3BhdGhuYW1lfSR7c2VhcmNofSR7aGFzaH1gXG59XG5cbmV4cG9ydCBjb25zdCB1cmxPYmplY3RLZXlzID0gW1xuICAnYXV0aCcsXG4gICdoYXNoJyxcbiAgJ2hvc3QnLFxuICAnaG9zdG5hbWUnLFxuICAnaHJlZicsXG4gICdwYXRoJyxcbiAgJ3BhdGhuYW1lJyxcbiAgJ3BvcnQnLFxuICAncHJvdG9jb2wnLFxuICAncXVlcnknLFxuICAnc2VhcmNoJyxcbiAgJ3NsYXNoZXMnLFxuXVxuXG5leHBvcnQgZnVuY3Rpb24gZm9ybWF0V2l0aFZhbGlkYXRpb24odXJsOiBVcmxPYmplY3QpOiBzdHJpbmcge1xuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcpIHtcbiAgICBpZiAodXJsICE9PSBudWxsICYmIHR5cGVvZiB1cmwgPT09ICdvYmplY3QnKSB7XG4gICAgICBPYmplY3Qua2V5cyh1cmwpLmZvckVhY2goKGtleSkgPT4ge1xuICAgICAgICBpZiAoIXVybE9iamVjdEtleXMuaW5jbHVkZXMoa2V5KSkge1xuICAgICAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgICAgIGBVbmtub3duIGtleSBwYXNzZWQgdmlhIHVybE9iamVjdCBpbnRvIHVybC5mb3JtYXQ6ICR7a2V5fWBcbiAgICAgICAgICApXG4gICAgICAgIH1cbiAgICAgIH0pXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGZvcm1hdFVybCh1cmwpXG59XG4iXSwibmFtZXMiOlsiZm9ybWF0VXJsIiwiZm9ybWF0V2l0aFZhbGlkYXRpb24iLCJ1cmxPYmplY3RLZXlzIiwic2xhc2hlZFByb3RvY29scyIsInVybE9iaiIsImF1dGgiLCJob3N0bmFtZSIsInByb3RvY29sIiwicGF0aG5hbWUiLCJoYXNoIiwicXVlcnkiLCJob3N0IiwiZW5jb2RlVVJJQ29tcG9uZW50IiwicmVwbGFjZSIsImluZGV4T2YiLCJwb3J0IiwiU3RyaW5nIiwicXVlcnlzdHJpbmciLCJ1cmxRdWVyeVRvU2VhcmNoUGFyYW1zIiwic2VhcmNoIiwiZW5kc1dpdGgiLCJzbGFzaGVzIiwidGVzdCIsInVybCIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsIk9iamVjdCIsImtleXMiLCJmb3JFYWNoIiwia2V5IiwiaW5jbHVkZXMiLCJjb25zb2xlIiwid2FybiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/styled-jsx/dist/index/index.js ***!
  \*****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n__webpack_require__(/*! client-only */ \"(app-pages-browser)/./node_modules/next/dist/compiled/client-only/index.js\");\nvar React = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === 'object' && 'default' in e ? e : {\n        'default': e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n_c = React__default;\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && \"development\" === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        }) // Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        }) // filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    _s();\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState({\n        \"StyleRegistry.useState[ref]\": function() {\n            return rootRegistry || configuredRegistry || createStyleRegistry();\n        }\n    }[\"StyleRegistry.useState[ref]\"]), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\n_s(StyleRegistry, \"F6PIZFsaWgcE6rBNmd+Zkq3zRoY=\");\n_c1 = StyleRegistry;\nfunction useStyleRegistry() {\n    _s1();\n    return React.useContext(StyleSheetContext);\n}\n_s1(useStyleRegistry, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    _s2();\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect({\n        \"JSXStyle.useInsertionEffect\": function() {\n            registry.add(props);\n            return ({\n                \"JSXStyle.useInsertionEffect\": function() {\n                    registry.remove(props);\n                }\n            })[\"JSXStyle.useInsertionEffect\"];\n        // props.children can be string[], will be striped since id is identical\n        }\n    }[\"JSXStyle.useInsertionEffect\"], [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\n_s2(JSXStyle, \"48Sqj1BUqkshsPdz6NEWXDn8pF4=\", false, function() {\n    return [\n        useStyleRegistry,\n        useInsertionEffect\n    ];\n});\n_c2 = JSXStyle;\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"React__default\");\n$RefreshReg$(_c1, \"StyleRegistry\");\n$RefreshReg$(_c2, \"JSXStyle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/styled-jsx/dist/index/index.js\n"));

/***/ })

});