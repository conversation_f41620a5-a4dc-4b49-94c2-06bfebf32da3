/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user-api-keys/route";
exports.ids = ["app/api/user-api-keys/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_user_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user-api-keys/route.ts */ \"(rsc)/./src/app/api/user-api-keys/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user-api-keys/route\",\n        pathname: \"/api/user-api-keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/user-api-keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\user-api-keys\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_user_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user-api-keys/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/user-api-keys/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/userApiKeys/apiKeyGenerator */ \"(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n// Validation schema for creating API keys\nconst CreateApiKeySchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    custom_api_config_id: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().uuid(),\n    key_name: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1).max(100),\n    expires_at: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().datetime().optional()\n});\n// POST /api/user-api-keys - Generate a new API key\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Parse and validate request body\n        const body = await request.json();\n        const validatedData = CreateApiKeySchema.parse(body);\n        // Check if user owns the custom API config\n        const { data: config, error: configError } = await supabase.from('custom_api_configs').select('id, name, user_id').eq('id', validatedData.custom_api_config_id).eq('user_id', user.id).single();\n        if (configError || !config) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Custom API configuration not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        // Get user's subscription tier from subscriptions table\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const subscriptionTier = subscription?.tier || 'free';\n        // Check current API key count for this user\n        const { count: currentKeyCount } = await supabase.from('user_generated_api_keys').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', user.id).eq('status', 'active');\n        // Validate subscription limits\n        const limitCheck = _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.validateSubscriptionLimits(subscriptionTier, currentKeyCount || 0);\n        if (!limitCheck.allowed) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: limitCheck.message\n            }, {\n                status: 403\n            });\n        }\n        // Check for duplicate key name within the config\n        const { data: existingKey } = await supabase.from('user_generated_api_keys').select('id').eq('custom_api_config_id', validatedData.custom_api_config_id).eq('key_name', validatedData.key_name).eq('status', 'active').single();\n        if (existingKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'An API key with this name already exists for this configuration'\n            }, {\n                status: 409\n            });\n        }\n        // Generate the API key\n        const { fullKey, prefix, secretPart, hash } = await _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.generateApiKey();\n        const encryptedSuffix = await _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.encryptSuffix(secretPart);\n        // Prepare the API key data with default settings\n        const apiKeyData = {\n            user_id: user.id,\n            custom_api_config_id: validatedData.custom_api_config_id,\n            key_name: validatedData.key_name,\n            key_prefix: prefix,\n            key_hash: hash,\n            encrypted_key_suffix: encryptedSuffix,\n            permissions: {\n                chat: true,\n                streaming: true,\n                all_models: true\n            },\n            allowed_ips: [],\n            allowed_domains: [],\n            expires_at: validatedData.expires_at || null\n        };\n        // Insert the API key into the database\n        const { data: createdKey, error: insertError } = await supabase.from('user_generated_api_keys').insert(apiKeyData).select().single();\n        if (insertError) {\n            console.error('Error creating API key:', insertError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create API key'\n            }, {\n                status: 500\n            });\n        }\n        // Return the response with the full API key (only shown once)\n        const response = {\n            id: createdKey.id,\n            key_name: createdKey.key_name,\n            api_key: fullKey,\n            key_prefix: createdKey.key_prefix,\n            permissions: createdKey.permissions,\n            created_at: createdKey.created_at,\n            expires_at: createdKey.expires_at\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request data',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error('Error in POST /api/user-api-keys:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/user-api-keys - List user's API keys\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get query parameters\n        const { searchParams } = new URL(request.url);\n        const configId = searchParams.get('config_id');\n        // Build query\n        let query = supabase.from('user_generated_api_keys').select(`\n        id,\n        key_name,\n        key_prefix,\n        encrypted_key_suffix,\n        permissions,\n        allowed_ips,\n        allowed_domains,\n        total_requests,\n        last_used_at,\n        status,\n        expires_at,\n        created_at,\n        custom_api_configs!inner(\n          id,\n          name\n        )\n      `).eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        // Filter by config if specified\n        if (configId) {\n            query = query.eq('custom_api_config_id', configId);\n        }\n        const { data: apiKeys, error } = await query;\n        if (error) {\n            console.error('Error fetching API keys:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch API keys'\n            }, {\n                status: 500\n            });\n        }\n        // Transform the data to include masked keys\n        const transformedKeys = await Promise.all(apiKeys.map(async (key)=>({\n                ...key,\n                masked_key: await _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.createMaskedKey(key.key_prefix, key.encrypted_key_suffix),\n                // Remove sensitive data\n                encrypted_key_suffix: undefined\n            })));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            api_keys: transformedKeys\n        });\n    } catch (error) {\n        console.error('Error in GET /api/user-api-keys:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user-api-keys/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n// Web Crypto API compatible encryption for Edge Runtime\nconst ALGORITHM = 'AES-GCM';\nconst IV_LENGTH = 12; // Recommended for GCM\n// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)\nconst ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;\nconsole.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);\nconsole.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);\nif (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {\n    throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');\n}\n// Convert hex string to Uint8Array for Web Crypto API\nfunction hexToUint8Array(hex) {\n    const bytes = new Uint8Array(hex.length / 2);\n    for(let i = 0; i < hex.length; i += 2){\n        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);\n    }\n    return bytes;\n}\n// Convert Uint8Array to hex string\nfunction uint8ArrayToHex(bytes) {\n    return Array.from(bytes, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n}\nconst keyBytes = hexToUint8Array(ROKEY_ENCRYPTION_KEY_FROM_ENV);\nasync function encrypt(text) {\n    if (typeof text !== 'string' || text.length === 0) {\n        throw new Error('Encryption input must be a non-empty string.');\n    }\n    // Generate random IV\n    const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));\n    // Import the key for Web Crypto API\n    const cryptoKey = await crypto.subtle.importKey('raw', keyBytes, {\n        name: ALGORITHM\n    }, false, [\n        'encrypt'\n    ]);\n    // Encrypt the text\n    const encodedText = new TextEncoder().encode(text);\n    const encryptedBuffer = await crypto.subtle.encrypt({\n        name: ALGORITHM,\n        iv: iv\n    }, cryptoKey, encodedText);\n    const encryptedArray = new Uint8Array(encryptedBuffer);\n    // For AES-GCM, the auth tag is included in the encrypted data (last 16 bytes)\n    const encryptedData = encryptedArray.slice(0, -16);\n    const authTag = encryptedArray.slice(-16);\n    // Return IV:authTag:encryptedData format\n    return `${uint8ArrayToHex(iv)}:${uint8ArrayToHex(authTag)}:${uint8ArrayToHex(encryptedData)}`;\n}\nasync function decrypt(encryptedText) {\n    if (typeof encryptedText !== 'string' || encryptedText.length === 0) {\n        throw new Error('Decryption input must be a non-empty string.');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');\n    }\n    const iv = hexToUint8Array(parts[0]);\n    const authTag = hexToUint8Array(parts[1]);\n    const encryptedData = hexToUint8Array(parts[2]);\n    if (iv.length !== IV_LENGTH) {\n        throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);\n    }\n    if (authTag.length !== 16) {\n        throw new Error(`Invalid authTag length. Expected 16 bytes.`);\n    }\n    // Import the key for Web Crypto API\n    const cryptoKey = await crypto.subtle.importKey('raw', keyBytes, {\n        name: ALGORITHM\n    }, false, [\n        'decrypt'\n    ]);\n    // Combine encrypted data and auth tag for Web Crypto API\n    const combinedData = new Uint8Array(encryptedData.length + authTag.length);\n    combinedData.set(encryptedData);\n    combinedData.set(authTag, encryptedData.length);\n    // Decrypt the data\n    const decryptedBuffer = await crypto.subtle.decrypt({\n        name: ALGORITHM,\n        iv: iv\n    }, cryptoKey, combinedData);\n    return new TextDecoder().decode(decryptedBuffer);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts":
/*!************************************************!*\
  !*** ./src/lib/userApiKeys/apiKeyGenerator.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyGenerator: () => (/* binding */ ApiKeyGenerator)\n/* harmony export */ });\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n\nclass ApiKeyGenerator {\n    static{\n        this.KEY_PREFIX = 'rk_live_';\n    }\n    static{\n        this.RANDOM_PART_LENGTH = 8 // hex chars for middle part\n        ;\n    }\n    static{\n        this.SECRET_PART_LENGTH = 32 // chars for secret part\n        ;\n    }\n    /**\n   * Generates a new API key with the format: rk_live_{8_hex_chars}_{32_random_chars}\n   * @returns Object containing the full key, prefix, and secret parts\n   */ static async generateApiKey() {\n        // Generate random hex for the middle part (visible in prefix)\n        const randomBytes = crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH / 2));\n        const randomHex = Array.from(randomBytes, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n        // Generate random alphanumeric for the secret part\n        const secretPart = this.generateRandomString(this.SECRET_PART_LENGTH);\n        // Construct the full key\n        const prefix = `${this.KEY_PREFIX}${randomHex}`;\n        const fullKey = `${prefix}_${secretPart}`;\n        // Generate hash for storage\n        const hash = await this.hashApiKey(fullKey);\n        return {\n            fullKey,\n            prefix,\n            secretPart,\n            hash\n        };\n    }\n    /**\n   * Generates a cryptographically secure random string\n   * @param length Length of the string to generate\n   * @returns Random alphanumeric string\n   */ static generateRandomString(length) {\n        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n        let result = '';\n        for(let i = 0; i < length; i++){\n            const randomBytes = crypto.getRandomValues(new Uint8Array(1));\n            const randomIndex = randomBytes[0] % chars.length;\n            result += chars[randomIndex];\n        }\n        return result;\n    }\n    /**\n   * Creates a SHA-256 hash of the API key for secure storage\n   * @param apiKey The full API key to hash\n   * @returns SHA-256 hash as hex string\n   */ static async hashApiKey(apiKey) {\n        const encoder = new TextEncoder();\n        const data = encoder.encode(apiKey);\n        const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n        const hashArray = new Uint8Array(hashBuffer);\n        return Array.from(hashArray, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n    }\n    /**\n   * Validates the format of an API key\n   * @param apiKey The API key to validate\n   * @returns True if the format is valid\n   */ static isValidFormat(apiKey) {\n        const pattern = new RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`);\n        return pattern.test(apiKey);\n    }\n    /**\n   * Extracts the prefix from a full API key\n   * @param apiKey The full API key\n   * @returns The prefix part (e.g., \"rk_live_abc12345\")\n   */ static extractPrefix(apiKey) {\n        const parts = apiKey.split('_');\n        if (parts.length >= 3) {\n            return `${parts[0]}_${parts[1]}_${parts[2]}`;\n        }\n        return '';\n    }\n    /**\n   * Encrypts the suffix part of an API key for partial display\n   * @param secretPart The secret part of the API key\n   * @returns Encrypted suffix for storage\n   */ static async encryptSuffix(secretPart) {\n        // Take last 4 characters for display purposes\n        const suffix = secretPart.slice(-4);\n        return await (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_0__.encrypt)(suffix);\n    }\n    /**\n   * Decrypts the suffix for display\n   * @param encryptedSuffix The encrypted suffix from database\n   * @returns Decrypted suffix for display\n   */ static async decryptSuffix(encryptedSuffix) {\n        try {\n            return await (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_0__.decrypt)(encryptedSuffix);\n        } catch (error) {\n            console.error('Failed to decrypt API key suffix:', error);\n            // Return a placeholder that looks like the last 4 chars\n            return 'xxxx';\n        }\n    }\n    /**\n   * Creates a masked version of the API key for display\n   * @param prefix The key prefix\n   * @param encryptedSuffix The encrypted suffix\n   * @returns Masked key for display (e.g., \"rk_live_abc12345_****xyz\")\n   */ static async createMaskedKey(prefix, encryptedSuffix) {\n        const suffix = await this.decryptSuffix(encryptedSuffix);\n        // Show 4 chars at the end, mask the rest (32 - 4 = 28 chars to mask)\n        const maskedLength = this.SECRET_PART_LENGTH - 4;\n        return `${prefix}_${'*'.repeat(maskedLength)}${suffix}`;\n    }\n    /**\n   * Validates subscription tier limits for API key generation\n   * @param subscriptionTier User's subscription tier\n   * @param currentKeyCount Current number of API keys for the user\n   * @returns Object indicating if generation is allowed and any limits\n   */ static validateSubscriptionLimits(subscriptionTier, currentKeyCount) {\n        // User-generated API keys limits (separate from API keys per config)\n        // These are total API keys a user can generate across all their configs\n        const limits = {\n            free: 3,\n            starter: 50,\n            professional: 999999,\n            enterprise: 999999 // Enterprise users get unlimited user-generated API keys\n        };\n        const limit = limits[subscriptionTier] || limits.free;\n        if (currentKeyCount >= limit) {\n            return {\n                allowed: false,\n                limit,\n                message: `You have reached the maximum number of user-generated API keys (${limit}) for your ${subscriptionTier} plan.`\n            };\n        }\n        return {\n            allowed: true,\n            limit\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();