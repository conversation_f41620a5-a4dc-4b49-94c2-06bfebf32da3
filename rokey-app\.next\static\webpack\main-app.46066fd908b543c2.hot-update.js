"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/client/app-find-source-map-url.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findSourceMapURL\", ({\n    enumerable: true,\n    get: function() {\n        return findSourceMapURL;\n    }\n}));\nconst basePath =  false || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nconst findSourceMapURL =  true ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : 0;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-find-source-map-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/app-link-gc.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"linkGc\", ({\n    enumerable: true,\n    get: function() {\n        return linkGc;\n    }\n}));\nfunction linkGc() {\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const callback = (mutationList)=>{\n            for (const mutation of mutationList){\n                if (mutation.type === 'childList') {\n                    for (const node of mutation.addedNodes){\n                        if ('tagName' in node && node.tagName === 'LINK') {\n                            var _link_dataset_precedence;\n                            const link = node;\n                            if ((_link_dataset_precedence = link.dataset.precedence) == null ? void 0 : _link_dataset_precedence.startsWith('next')) {\n                                const href = link.getAttribute('href');\n                                if (href) {\n                                    const [resource, version] = href.split('?v=', 2);\n                                    if (version) {\n                                        const currentOrigin = window.location.origin;\n                                        const allLinks = [\n                                            ...document.querySelectorAll('link[href^=\"' + resource + '\"]'),\n                                            // It's possible that the resource is a full URL or only pathname,\n                                            // so we need to remove the alternative href as well.\n                                            ...document.querySelectorAll('link[href^=\"' + (resource.startsWith(currentOrigin) ? resource.slice(currentOrigin.length) : currentOrigin + resource) + '\"]')\n                                        ];\n                                        for (const otherLink of allLinks){\n                                            var _otherLink_dataset_precedence;\n                                            if ((_otherLink_dataset_precedence = otherLink.dataset.precedence) == null ? void 0 : _otherLink_dataset_precedence.startsWith('next')) {\n                                                const otherHref = otherLink.getAttribute('href');\n                                                if (otherHref) {\n                                                    const [, otherVersion] = otherHref.split('?v=', 2);\n                                                    if (!otherVersion || +otherVersion < +version) {\n                                                        // Delay the removal of the stylesheet to avoid FOUC\n                                                        // caused by `@font-face` rules, as they seem to be\n                                                        // a couple of ticks delayed between the old and new\n                                                        // styles being swapped even if the font is cached.\n                                                        setTimeout(()=>{\n                                                            otherLink.remove();\n                                                        }, 5);\n                                                        const preloadLink = document.querySelector('link[rel=\"preload\"][as=\"style\"][href=\"' + otherHref + '\"]');\n                                                        if (preloadLink) {\n                                                            preloadLink.remove();\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        // Create an observer instance linked to the callback function\n        const observer = new MutationObserver(callback);\n        observer.observe(document.head, {\n            childList: true\n        });\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-link-gc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-instance.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createMutableActionQueue: function() {\n        return createMutableActionQueue;\n    },\n    dispatchNavigateAction: function() {\n        return dispatchNavigateAction;\n    },\n    dispatchTraverseAction: function() {\n        return dispatchTraverseAction;\n    },\n    getCurrentAppRouterState: function() {\n        return getCurrentAppRouterState;\n    },\n    publicAppRouterInstance: function() {\n        return publicAppRouterInstance;\n    }\n});\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _routerreducer = __webpack_require__(/*! ./router-reducer/router-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nfunction runRemainingActions(actionQueue, setState) {\n    if (actionQueue.pending !== null) {\n        actionQueue.pending = actionQueue.pending.next;\n        if (actionQueue.pending !== null) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            runAction({\n                actionQueue,\n                action: actionQueue.pending,\n                setState\n            });\n        } else {\n            // No more actions are pending, check if a refresh is needed\n            if (actionQueue.needsRefresh) {\n                actionQueue.needsRefresh = false;\n                actionQueue.dispatch({\n                    type: _routerreducertypes.ACTION_REFRESH,\n                    origin: window.location.origin\n                }, setState);\n            }\n        }\n    }\n}\nasync function runAction(param) {\n    let { actionQueue, action, setState } = param;\n    const prevState = actionQueue.state;\n    actionQueue.pending = action;\n    const payload = action.payload;\n    const actionResult = actionQueue.action(prevState, payload);\n    function handleResult(nextState) {\n        // if we discarded this action, the state should also be discarded\n        if (action.discarded) {\n            return;\n        }\n        actionQueue.state = nextState;\n        runRemainingActions(actionQueue, setState);\n        action.resolve(nextState);\n    }\n    // if the action is a promise, set up a callback to resolve it\n    if ((0, _isthenable.isThenable)(actionResult)) {\n        actionResult.then(handleResult, (err)=>{\n            runRemainingActions(actionQueue, setState);\n            action.reject(err);\n        });\n    } else {\n        handleResult(actionResult);\n    }\n}\nfunction dispatchAction(actionQueue, payload, setState) {\n    let resolvers = {\n        resolve: setState,\n        reject: ()=>{}\n    };\n    // most of the action types are async with the exception of restore\n    // it's important that restore is handled quickly since it's fired on the popstate event\n    // and we don't want to add any delay on a back/forward nav\n    // this only creates a promise for the async actions\n    if (payload.type !== _routerreducertypes.ACTION_RESTORE) {\n        // Create the promise and assign the resolvers to the object.\n        const deferredPromise = new Promise((resolve, reject)=>{\n            resolvers = {\n                resolve,\n                reject\n            };\n        });\n        (0, _react.startTransition)(()=>{\n            // we immediately notify React of the pending promise -- the resolver is attached to the action node\n            // and will be called when the associated action promise resolves\n            setState(deferredPromise);\n        });\n    }\n    const newAction = {\n        payload,\n        next: null,\n        resolve: resolvers.resolve,\n        reject: resolvers.reject\n    };\n    // Check if the queue is empty\n    if (actionQueue.pending === null) {\n        // The queue is empty, so add the action and start it immediately\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else if (payload.type === _routerreducertypes.ACTION_NAVIGATE || payload.type === _routerreducertypes.ACTION_RESTORE) {\n        // Navigations (including back/forward) take priority over any pending actions.\n        // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n        actionQueue.pending.discarded = true;\n        // The rest of the current queue should still execute after this navigation.\n        // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n        newAction.next = actionQueue.pending.next;\n        // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n        if (actionQueue.pending.payload.type === _routerreducertypes.ACTION_SERVER_ACTION) {\n            actionQueue.needsRefresh = true;\n        }\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else {\n        // The queue is not empty, so add the action to the end of the queue\n        // It will be started by runRemainingActions after the previous action finishes\n        if (actionQueue.last !== null) {\n            actionQueue.last.next = newAction;\n        }\n        actionQueue.last = newAction;\n    }\n}\nlet globalActionQueue = null;\nfunction createMutableActionQueue(initialState, instrumentationHooks) {\n    const actionQueue = {\n        state: initialState,\n        dispatch: (payload, setState)=>dispatchAction(actionQueue, payload, setState),\n        action: async (state, action)=>{\n            const result = (0, _routerreducer.reducer)(state, action);\n            return result;\n        },\n        pending: null,\n        last: null,\n        onRouterTransitionStart: instrumentationHooks !== null && typeof instrumentationHooks.onRouterTransitionStart === 'function' ? instrumentationHooks.onRouterTransitionStart : null\n    };\n    if (true) {\n        // The action queue is lazily created on hydration, but after that point\n        // it doesn't change. So we can store it in a global rather than pass\n        // it around everywhere via props/context.\n        if (globalActionQueue !== null) {\n            throw Object.defineProperty(new Error('Internal Next.js Error: createMutableActionQueue was called more ' + 'than once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E624\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        globalActionQueue = actionQueue;\n    }\n    return actionQueue;\n}\nfunction getCurrentAppRouterState() {\n    return globalActionQueue !== null ? globalActionQueue.state : null;\n}\nfunction getAppRouterActionQueue() {\n    if (globalActionQueue === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return globalActionQueue;\n}\nfunction getProfilingHookForOnNavigationStart() {\n    if (globalActionQueue !== null) {\n        return globalActionQueue.onRouterTransitionStart;\n    }\n    return null;\n}\nfunction dispatchNavigateAction(href, navigateType, shouldScroll, linkInstanceRef) {\n    // TODO: This stuff could just go into the reducer. Leaving as-is for now\n    // since we're about to rewrite all the router reducer stuff anyway.\n    const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n    if (false) {}\n    (0, _links.setLinkForCurrentNavigation)(linkInstanceRef);\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, navigateType);\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_NAVIGATE,\n        url,\n        isExternalUrl: (0, _approuter.isExternalURL)(url),\n        locationSearch: location.search,\n        shouldScroll,\n        navigateType,\n        allowAliasing: true\n    });\n}\nfunction dispatchTraverseAction(href, tree) {\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, 'traverse');\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_RESTORE,\n        url: new URL(href),\n        tree\n    });\n}\nconst publicAppRouterInstance = {\n    back: ()=>window.history.back(),\n    forward: ()=>window.history.forward(),\n    prefetch:  false ? // cache. So we don't need to dispatch an action.\n    0 : (href, options)=>{\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue();\n        const url = (0, _approuter.createPrefetchURL)(href);\n        if (url !== null) {\n            var _options_kind;\n            // The prefetch reducer doesn't actually update any state or\n            // trigger a rerender. It just writes to a mutable cache. So we\n            // shouldn't bother calling setState/dispatch; we can just re-run\n            // the reducer directly using the current state.\n            // TODO: Refactor this away from a \"reducer\" so it's\n            // less confusing.\n            (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                type: _routerreducertypes.ACTION_PREFETCH,\n                url,\n                kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n            });\n        }\n    },\n    replace: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'replace', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    push: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'push', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    refresh: ()=>{\n        (0, _react.startTransition)(()=>{\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_REFRESH,\n                origin: window.location.origin\n            });\n        });\n    },\n    hmrRefresh: ()=>{\n        if (false) {} else {\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_HMR_REFRESH,\n                    origin: window.location.origin\n                });\n            });\n        }\n    }\n};\n// Exists for debugging purposes. Don't use in application code.\nif ( true && window.next) {\n    window.next.router = publicAppRouterInstance;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-instance.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    },\n    isExternalURL: function() {\n        return isExternalURL;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1\n    };\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    let { actionQueue, assetPrefix, globalError } = param;\n    const state = (0, _useactionqueue.useActionQueue)(actionQueue);\n    const { canonicalUrl } = state;\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = state;\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: _approuterinstance.publicAppRouterInstance,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, []);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                // TODO: This should access the router methods directly, rather than\n                // go through the public interface.\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    _approuterinstance.publicAppRouterInstance.push(url, {});\n                } else {\n                    _approuterinstance.publicAppRouterInstance.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, []);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = state;\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location = window.location;\n            if (pushRef.pendingPush) {\n                location.assign(canonicalUrl);\n            } else {\n                location.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                (0, _approuterinstance.dispatchTraverseAction)(window.location.href, event.state.__PRIVATE_NEXTJS_INTERNALS_TREE);\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, []);\n    const { cache, tree, nextUrl, focusAndScrollRef } = state;\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: state\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: _approuterinstance.publicAppRouterInstance,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleConsoleError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { navigatedAt, initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading,\n        navigatedAt\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(navigatedAt, cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createFetch: function() {\n        return createFetch;\n    },\n    createFromNextReadableStream: function() {\n        return createFromNextReadableStream;\n    },\n    fetchServerResponse: function() {\n        return fetchServerResponse;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _appcallserver = __webpack_require__(/*! ../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _appbuildid = __webpack_require__(/*! ../../app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\nconst _setcachebustingsearchparam = __webpack_require__(/*! ./set-cache-busting-search-param */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js\");\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(url).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nlet abortController = new AbortController();\nif (true) {\n    // Abort any in-flight requests when the page is unloaded, e.g. due to\n    // reloading the page or performing hard navigations. This allows us to ignore\n    // what would otherwise be a thrown TypeError when the browser cancels the\n    // requests.\n    window.addEventListener('pagehide', ()=>{\n        abortController.abort();\n    });\n    // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n    // and the JavaScript execution context is restored by the browser.\n    window.addEventListener('pageshow', ()=>{\n        abortController = new AbortController();\n    });\n}\nasync function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [_approuterheaders.RSC_HEADER]: '1',\n        // Provide the current router state\n        [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(JSON.stringify(flightRouterState))\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === _routerreducertypes.PrefetchKind.AUTO) {\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if ( true && options.isHmrRefresh) {\n        headers[_approuterheaders.NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[_approuterheaders.NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === _routerreducertypes.PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        if (false) {}\n        const res = await createFetch(url, headers, fetchPriority, abortController.signal);\n        const responseUrl = urlToUrlWithoutFlightMarker(res.url);\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(_approuterheaders.NEXT_URL));\n        const postponed = !!res.headers.get(_approuterheaders.NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeaderSeconds = res.headers.get(_approuterheaders.NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeaderSeconds !== null ? parseInt(staleTimeHeaderSeconds, 10) * 1000 : -1;\n        let isFlightResponse = contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER);\n        if (false) {}\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (true) {\n            await (__webpack_require__(/*! ../react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\").waitForWebpackRuntimeHotUpdate)();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if ((0, _appbuildid.getAppBuildId)() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        if (!abortController.signal.aborted) {\n            console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        }\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nfunction createFetch(url, headers, fetchPriority, signal) {\n    const fetchUrl = new URL(url);\n    // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n    // cache busting search param) from the request so they're\n    // maximally cacheable.\n    (0, _setcachebustingsearchparam.setCacheBustingSearchParam)(fetchUrl, headers);\n    if (false) {}\n    if (false) {}\n    return fetch(fetchUrl, {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined,\n        signal\n    });\n}\nfunction createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer: _appcallserver.callServer,\n        findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fetch-server-response.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js ***!
  \********************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"handleSegmentMismatch\", ({\n    enumerable: true,\n    get: function() {\n        return handleSegmentMismatch;\n    }\n}));\nconst _navigatereducer = __webpack_require__(/*! ./reducers/navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nfunction handleSegmentMismatch(state, action, treePatch) {\n    if (true) {\n        console.warn('Performing hard navigation because your application experienced an unrecoverable error. If this keeps occurring, please file a Next.js issue.\\n\\n' + 'Reason: Segment mismatch\\n' + (\"Last Action: \" + action.type + \"\\n\\n\") + (\"Current Tree: \" + JSON.stringify(state.tree) + \"\\n\\n\") + (\"Tree Patch Payload: \" + JSON.stringify(treePatch)));\n    }\n    return (0, _navigatereducer.handleExternalUrl)(state, {}, state.canonicalUrl, true);\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=handle-segment-mismatch.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_STALETIME_MS: function() {\n        return DYNAMIC_STALETIME_MS;\n    },\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    const navigatedAt = Date.now();\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(navigatedAt, currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/use-action-queue.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    dispatchAppRouterAction: function() {\n        return dispatchAppRouterAction;\n    },\n    useActionQueue: function() {\n        return useActionQueue;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch = null;\nfunction dispatchAppRouterAction(action) {\n    if (dispatch === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    dispatch(action);\n}\nfunction useActionQueue(actionQueue) {\n    _s();\n    const [state, setState] = _react.default.useState(actionQueue.state);\n    // Because of a known issue that requires to decode Flight streams inside the\n    // render phase, we have to be a bit clever and assign the dispatch method to\n    // a module-level variable upon initialization. The useState hook in this\n    // module only exists to synchronize state that lives outside of React.\n    // Ideally, what we'd do instead is pass the state as a prop to root.render;\n    // this is conceptually how we're modeling the app router state, despite the\n    // weird implementation details.\n    if (true) {\n        const useSyncDevRenderIndicator = (__webpack_require__(/*! ./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js\").useSyncDevRenderIndicator);\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const syncDevRenderIndicator = useSyncDevRenderIndicator();\n        dispatch = (action)=>{\n            syncDevRenderIndicator(()=>{\n                actionQueue.dispatch(action, setState);\n            });\n        };\n    } else {}\n    return (0, _isthenable.isThenable)(state) ? (0, _react.use)(state) : state;\n}\n_s(useActionQueue, \"Rp0Tj1zyE8LTecjN/cjTzn46xPo=\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-action-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js ***!
  \********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    AppRouterContext: function() {\n        return AppRouterContext;\n    },\n    GlobalLayoutRouterContext: function() {\n        return GlobalLayoutRouterContext;\n    },\n    LayoutRouterContext: function() {\n        return LayoutRouterContext;\n    },\n    MissingSlotContext: function() {\n        return MissingSlotContext;\n    },\n    TemplateContext: function() {\n        return TemplateContext;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst AppRouterContext = _react.default.createContext(null);\nconst LayoutRouterContext = _react.default.createContext(null);\nconst GlobalLayoutRouterContext = _react.default.createContext(null);\nconst TemplateContext = _react.default.createContext(null);\nif (true) {\n    AppRouterContext.displayName = 'AppRouterContext';\n    LayoutRouterContext.displayName = 'LayoutRouterContext';\n    GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext';\n    TemplateContext.displayName = 'TemplateContext';\n}\nconst MissingSlotContext = _react.default.createContext(new Set()); //# sourceMappingURL=app-router-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HeadManagerContext\", ({\n    enumerable: true,\n    get: function() {\n        return HeadManagerContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/next/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst HeadManagerContext = _react.default.createContext({});\nif (true) {\n    HeadManagerContext.displayName = 'HeadManagerContext';\n} //# sourceMappingURL=head-manager-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9oZWFkLW1hbmFnZXItY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7O3NEQUVhQTs7O2VBQUFBOzs7OzRFQUZLO0FBRVgsTUFBTUEscUJBVVJDLE9BQUFBLE9BQUssQ0FBQ0MsYUFBYSxDQUFDLENBQUM7QUFFMUIsSUFBSUMsSUFBb0IsRUFBbUI7SUFDekNILG1CQUFtQk0sV0FBVyxHQUFHO0FBQ25DIiwic291cmNlcyI6WyJDOlxcc3JjXFxzaGFyZWRcXGxpYlxcaGVhZC1tYW5hZ2VyLWNvbnRleHQuc2hhcmVkLXJ1bnRpbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgY29uc3QgSGVhZE1hbmFnZXJDb250ZXh0OiBSZWFjdC5Db250ZXh0PHtcbiAgdXBkYXRlSGVhZD86IChzdGF0ZTogYW55KSA9PiB2b2lkXG4gIG1vdW50ZWRJbnN0YW5jZXM/OiBhbnlcbiAgdXBkYXRlU2NyaXB0cz86IChzdGF0ZTogYW55KSA9PiB2b2lkXG4gIHNjcmlwdHM/OiBhbnlcbiAgZ2V0SXNTc3I/OiAoKSA9PiBib29sZWFuXG5cbiAgLy8gVXNlZCBpbiBhcHAgZGlyZWN0b3J5LCB0byByZW5kZXIgc2NyaXB0IHRhZ3MgYXMgc2VydmVyIGNvbXBvbmVudHMuXG4gIGFwcERpcj86IGJvb2xlYW5cbiAgbm9uY2U/OiBzdHJpbmdcbn0+ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh7fSlcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgSGVhZE1hbmFnZXJDb250ZXh0LmRpc3BsYXlOYW1lID0gJ0hlYWRNYW5hZ2VyQ29udGV4dCdcbn1cbiJdLCJuYW1lcyI6WyJIZWFkTWFuYWdlckNvbnRleHQiLCJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathParamsContext: function() {\n        return PathParamsContext;\n    },\n    PathnameContext: function() {\n        return PathnameContext;\n    },\n    SearchParamsContext: function() {\n        return SearchParamsContext;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst SearchParamsContext = (0, _react.createContext)(null);\nconst PathnameContext = (0, _react.createContext)(null);\nconst PathParamsContext = (0, _react.createContext)(null);\nif (true) {\n    SearchParamsContext.displayName = 'SearchParamsContext';\n    PathnameContext.displayName = 'PathnameContext';\n    PathParamsContext.displayName = 'PathParamsContext';\n} //# sourceMappingURL=hooks-client-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9ob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFPYUEsaUJBQWlCO2VBQWpCQTs7SUFEQUMsZUFBZTtlQUFmQTs7SUFEQUMsbUJBQW1CO2VBQW5CQTs7O21DQUhpQjtBQUd2QixNQUFNQSxzQkFBc0JDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQXNDO0FBQ2xFLE1BQU1GLGtCQUFrQkUsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBNkI7QUFDckQsTUFBTUgsb0JBQW9CRyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUE2QjtBQUU5RCxJQUFJQyxJQUFvQixFQUFtQjtJQUN6Q0Ysb0JBQW9CSyxXQUFXLEdBQUc7SUFDbENOLGdCQUFnQk0sV0FBVyxHQUFHO0lBQzlCUCxrQkFBa0JPLFdBQVcsR0FBRztBQUNsQyIsInNvdXJjZXMiOlsiQzpcXHNyY1xcc2hhcmVkXFxsaWJcXGhvb2tzLWNsaWVudC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgdHlwZSB7IFBhcmFtcyB9IGZyb20gJy4uLy4uL3NlcnZlci9yZXF1ZXN0L3BhcmFtcydcblxuZXhwb3J0IGNvbnN0IFNlYXJjaFBhcmFtc0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFVSTFNlYXJjaFBhcmFtcyB8IG51bGw+KG51bGwpXG5leHBvcnQgY29uc3QgUGF0aG5hbWVDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxzdHJpbmcgfCBudWxsPihudWxsKVxuZXhwb3J0IGNvbnN0IFBhdGhQYXJhbXNDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxQYXJhbXMgfCBudWxsPihudWxsKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBTZWFyY2hQYXJhbXNDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1NlYXJjaFBhcmFtc0NvbnRleHQnXG4gIFBhdGhuYW1lQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdQYXRobmFtZUNvbnRleHQnXG4gIFBhdGhQYXJhbXNDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1BhdGhQYXJhbXNDb250ZXh0J1xufVxuIl0sIm5hbWVzIjpbIlBhdGhQYXJhbXNDb250ZXh0IiwiUGF0aG5hbWVDb250ZXh0IiwiU2VhcmNoUGFyYW1zQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\n"));

/***/ })

});